# Component Migration Templates

## Migration Templates for Sync Pattern Updates

### Template 1: useSyncEvents → useSyncState Migration

**Before:**
```typescript
import { useSyncEvents } from '@/hooks/useSyncEvents';

// Basic sync event listening
useSyncEvents(callbackFunction);
```

**After:**
```typescript
import { useSyncState } from '@/hooks/useSyncEvents';

// Enhanced sync state monitoring with more context
useSyncState((syncState) => {
  // More precise: only trigger when sync actually completes
  if (!syncState.isProcessing && syncState.stats.pending === 0) {
    callbackFunction();
  }
});
```

### Template 2: Enhanced Session Sync Usage

**Before:**
```typescript
import { sessionSyncService } from '@/lib/services/sessionSync';

await sessionSyncService.processPendingSync();
```

**After:**
```typescript
import { sessionSyncService } from '@/lib/services/sessionSync';

// For frame-specific sync (better performance)
await sessionSyncService.processPendingSessionSync(frameId);

// For global sync with automatic cleanup
await sessionSyncService.syncToServer();
```

### Template 3: Sync Queue Operations (New Code)

**Before:**
```typescript
// Manual sync queue operations
await db.add('syncQueue', {
  operationType: 'create',
  objectType: 'frame',
  objectId: frameId,
  priority: 10,
  // ... manual setup
});
```

**After:**
```typescript
import { addFrameToSyncQueue, addCaptureToSyncQueue } from '@/lib/db/syncQueueHelper';

// Standardized operations
await addFrameToSyncQueue('create', frameId);
await addCaptureToSyncQueue('update', captureId, frameId);
```

## Component-Specific Migration Examples

### SessionCard.tsx ✅ (COMPLETED)
```typescript
// Enhanced sync state monitoring
useSyncState((syncState) => {
  if (!syncState.isProcessing && syncState.stats.pending === 0) {
    loadCaptures();
  }
});
```

### SyncStatus.tsx (RECOMMENDED)
```typescript
// Current: Mixed approach
useSyncEvents(updateStats);

// Enhanced: Direct progress monitoring
useSyncProgress((progress) => {
  if (progress) {
    setProgress(progress);
    setSyncing(progress.isProcessing);
  }
});
```

### FrameSyncStatus.tsx (RECOMMENDED)
```typescript
// Current: Generic sync events
useSyncEvents(updateStats);

// Enhanced: Frame-aware sync monitoring
useSyncState((syncState) => {
  // Only update when sync completes and might affect this frame
  if (!syncState.isProcessing) {
    updateStats();
  }
});
```

### SessionList.tsx (OPTIONAL)
```typescript
// Enhanced background sync
const loadSessions = useCallback(async () => {
  const frames = await loadSessionsWithFallback();
  setSessions(frames);
  
  // Use enhanced session sync for background operations
  if (navigator.onLine && !sessionSyncService.isSyncing) {
    sessionSyncService.syncToServer().catch(() => {
      // Silent background sync with automatic cleanup
    });
  }
}, []);
```

### HistoryPanel.tsx (OPTIONAL)
```typescript
// Add frame-specific sync capability
const handleFrameSync = useCallback(async () => {
  try {
    await sessionSyncService.processPendingSessionSync(frameId);
    await refreshCaptures();
  } catch (error) {
    console.error('Frame sync failed:', error);
  }
}, [frameId, refreshCaptures]);
```

## Migration Benefits by Component

| Component | Current Issue | Migration Benefit | Implementation |
|-----------|---------------|-------------------|----------------|
| **SessionCard** | Reloads on every sync event | Only reload when sync completes | ✅ **COMPLETED** |
| **SyncStatus** | Mixed event patterns | Direct progress monitoring | Replace `useSyncEvents` with `useSyncProgress` |
| **FrameSyncStatus** | Generic sync events | Frame-aware monitoring | Replace with `useSyncState` |
| **SessionList** | Basic sync service | Enhanced features + cleanup | Add enhanced session sync calls |
| **HistoryPanel** | No frame-specific sync | Frame-specific sync capability | Add `processPendingSessionSync` |

## Migration Testing Checklist

### For Each Migrated Component:
- [ ] **Functionality**: Component behavior unchanged
- [ ] **Performance**: Reduced unnecessary updates/reloads
- [ ] **Error Handling**: Graceful error handling maintained
- [ ] **User Experience**: No regression in UX
- [ ] **Memory**: No memory leaks from subscriptions

### Testing Commands:
```bash
# Frontend linting
npm run lint

# Manual testing
# 1. Test sync operations in the component
# 2. Verify UI updates correctly
# 3. Check browser console for errors
# 4. Monitor performance in dev tools
```

## Rollback Strategy

If any migration causes issues:

1. **Immediate Rollback**: Revert to previous pattern
2. **Hybrid Approach**: Keep both old and new patterns temporarily
3. **Gradual Migration**: Migrate one component at a time

Example hybrid approach:
```typescript
// Keep both patterns during testing
useSyncEvents(callbackFunction); // Legacy (reliable)
useSyncState((syncState) => {     // New (testing)
  if (!syncState.isProcessing) {
    // Test new pattern
  }
});
```

## Next Steps

1. **Test SessionCard.tsx**: Verify the completed migration works correctly
2. **Migrate SyncStatus.tsx**: High impact, low effort
3. **Migrate FrameSyncStatus.tsx**: Medium impact, low effort
4. **Optional Enhancements**: SessionList.tsx and HistoryPanel.tsx
5. **Monitor Performance**: Track improvements after each migration
