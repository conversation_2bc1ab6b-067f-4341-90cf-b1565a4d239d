# Object Detection System

A web-based real-time object detection system using YOLOv8n for identifying and analyzing objects in images and video streams. Despite the name "Weld Defect Detection," this is a general-purpose object detection application built with modern web technologies and optimized for performance.

## 🎯 Project Overview

### Core Functionality

#### Detection Modes
- **Live Detection**: Real-time object detection from webcam with performance metrics
- **Capture Mode**: Take photos with webcam, detect objects, and save results
- **Upload Mode**: Batch process multiple image files for object detection

#### Key Features
- **Frame Management**: Organize detection sessions into frames for better workflow organization
- **Detection History**: View and manage past detection results with thumbnails
- **Visual Results**: Display original images alongside annotated versions with bounding boxes
- **Performance Monitoring**: Real-time FPS, inference time, and detection statistics
- **Report Generation**: Export detection results as PDF reports

#### User Interface
- Responsive web interface with modern design
- Three-tab navigation (Capture/Upload/Live)
- Real-time video preview with square cropping (640x640)
- Side-by-side comparison of original vs. detected images
- Interactive detection history with selection capabilities

## 🎯 Technical Goals


### Usability
- Intuitive web interface accessible on desktop/mobile
- Drag-and-drop file upload
- One-click report generation
- Session management with frame organization

## 🛠️ Technology Stack

### Frontend Architecture
```
Next.js 15 + TypeScript + Modern React
├── 🎨 UI Framework: Tailwind CSS + shadcn/ui
├── 🧠 AI Inference: ONNX.js + WebGL/WASM
├── 🔄 Data Fetching: React Query (TanStack Query)
├── 🎬 Animations: Framer Motion
├── 📝 Forms: React Hook Form + Zod validation
└── 🧪 Testing: Jest + React Testing Library + Playwright
```

### Backend Architecture
```
FastAPI + Python 3.12+ + Async/Await
├── 🔧 API Framework: FastAPI + Pydantic v2
├── 🧠 AI Runtime: ONNX Runtime
├── 🗄️ Database: SQLite + SQLAlchemy ORM
├── ⚡ Cache & Queue: Redis + pub/sub
├── 🔌 Real-time: WebSocket + Socket.IO
├── 📁 File Storage: Configurable (Local/S3/GCS)
├── 🔐 Authentication: JWT + OAuth2
└── 🧪 Testing: pytest + pytest-asyncio
```

### AI/ML Pipeline
```
ONNX Universal Format (Optimized)
├── 📱 Client-side: ONNX.js + WebGL backend
├── 🖥️ Server-side: ONNX Runtime 
├── 🎯 Model: YOLOv8n (80 COCO classes)
├── 🔄 Smart Routing: Device capability detection
├── 📦 Model Management: Versioning + caching
└── ⚡ Optimization: batching
```

### Infrastructure & DevOps
```
Containerized Microservices
├── 🐳 Development: Docker Compose
├── ☁️ Staging: Railway/Render
├── 🚀 Production: Kubernetes + Helm
├── 📊 Monitoring: Grafana + Prometheus + Loki
├── 🔄 CI/CD: GitHub Actions
├── 🌐 CDN: Cloudflare/AWS CloudFront
└── 🔐 Security: HTTPS + CORS + Rate limiting
```

## 🏗️ Architecture Overview

### System Components

#### Frontend (Next.js)
- **Camera Interface**: WebRTC camera access with device selection
- **Image Processing**: Client-side inference with ONNX.js
- **Real-time UI**: WebSocket integration for live updates
- **File Management**: Drag-and-drop uploads with progress tracking
- **Report Generation**: Client-side PDF/CSV generation

#### Backend (FastAPI)
- **API Gateway**: RESTful endpoints + WebSocket handlers
- **Inference Engine**: Server-side ONNX Runtime fallback
- **Task Queue**: Redis-based async processing
- **Data Layer**: SQLite with connection pooling
- **File Storage**: Multi-provider abstraction layer

#### Database Schema
```sql
Users
├── Sessions (Detection frames)
├── Detections (Individual results)
├── ModelMetrics (Performance tracking)
└── Reports (Generated exports)
```

## 🔄 Data Flow

### Real-time Detection Flow
```mermaid
graph LR
    A[Camera] --> B[Browser]
    B --> C[ONNX.js]
    C --> D[Results]
    D --> E[WebSocket]
    E --> F[Backend]
    F --> G[Database]
```

### Upload Processing Flow
```mermaid
graph LR
    A[File Upload] --> B[FastAPI]
    B --> C[Redis Queue]
    C --> D[ONNX Runtime]
    D --> E[Results]
    E --> F[SQLite]
    F --> G[WebSocket Notify]
```

## 🚀 Performance Targets

### Client-side Inference (80% of users)
- **Latency**: 50-100ms total processing
- **Privacy**: Images never leave device
- **Offline**: Full functionality without internet
- **Scalability**: Unlimited concurrent users

### Server-side Fallback (20% of users)
- **Smart routing** for low-end devices
- **150-250ms** response time
- **Graceful degradation**
- **Auto-scaling** based on load

### System Performance
- **Concurrent Users**: 1000+ simultaneous
- **Throughput**: 10,000+ detections/hour
- **Uptime**: 99.9% availability
- **Response Time**: <100ms API responses

## 📁 Project Structure

```
project-root/
├── frontend/                 # Next.js application
│   ├── app/                 # App router pages
│   ├── components/          # Reusable components
│   ├── lib/                 # Utilities and AI models
│   ├── public/models/       # ONNX models and assets
│   └── types/               # TypeScript definitions
├── backend/                 # FastAPI application
│   ├── app/                 # Main application
│   ├── tests/               # Test suites
│   ├── migrations/          # Database migrations
│   └── requirements.txt     # Python dependencies
├── infrastructure/          # Deployment configs
│   ├── docker/              # Docker configurations
│   ├── k8s/                 # Kubernetes manifests
│   └── monitoring/          # Grafana/Prometheus configs
├── models/                  # AI model files
│   ├── yolov8n.onnx        # Optimized ONNX model
│   └── metadata.json       # Model configuration
└── docs/                    # Documentation
    ├── api/                 # API documentation
    ├── deployment/          # Deployment guides
    └── development/         # Development setup
```

## 🔧 Development Setup

### Prerequisites
- Node.js 18+ and npm
- Python 3.11+ and Poetry
- Docker and Docker Compose
- Git

### Quick Start
```bash
# Clone repository
git clone <repository-url>
cd object-detection-system

# Start development environment
docker-compose up -d

# Install frontend dependencies
cd frontend && npm install

# Install backend dependencies
cd backend && poetry install

# Run development servers
npm run dev          # Frontend (http://localhost:3000)
poetry run dev       # Backend (http://localhost:8000)
```

## 📊 Monitoring & Analytics

### Application Metrics
- Real-time inference performance
- User session analytics
- Model accuracy tracking
- System resource utilization

### Business Metrics
- Detection volume and trends
- User engagement patterns
- Feature usage statistics
- Error rates and performance

## 🔐 Security Features

- **Authentication**: JWT-based with refresh tokens
- **Authorization**: Role-based access control
- **Data Protection**: Encryption at rest and in transit
- **Privacy**: Client-side processing by default
- **Validation**: Input sanitization and validation
- **Rate Limiting**: API abuse prevention

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.

## 📞 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in the `/docs` folder
- Review the API documentation at `/api/docs`

---

**Built with ❤️ using modern web technologies for high-performance object detection**