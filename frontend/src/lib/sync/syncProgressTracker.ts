import type { SyncProgress, SyncProgressCallback, SyncQueueItem } from '@/lib/db/types';

/**
 * Sync Progress Tracker
 * 
 * Handles progress reporting, batch tracking, and performance metrics for sync operations.
 */
export class SyncProgressTracker {
  /**
   * Create initial progress object for sync operations
   */
  createInitialProgress(): SyncProgress {
    return {
      totalItems: 0,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      isProcessing: true,
      performanceMetrics: {
        itemsPerSecond: 0,
        averageBatchTime: 0,
        estimatedTimeRemaining: 0,
        startTime: Date.now()
      },
      errorSummary: {
        networkErrors: 0,
        authErrors: 0,
        serverErrors: 0,
        clientErrors: 0,
        unknownErrors: 0
      }
    };
  }

  /**
   * Clean up sync operation state and notify callbacks
   */
  cleanupSyncOperation(progress: SyncProgress, progressCallback?: SyncProgressCallback): void {
    progress.isProcessing = false;
    progress.currentItem = undefined;
    if (progressCallback) {
      progressCallback({ ...progress });
    }
  }

  /**
   * Create batch processing configuration
   */
  createBatchConfig(items: SyncQueueItem[], defaultBatchSize: number, maxBatchSize: number) {
    const batchSize = Math.min(defaultBatchSize, maxBatchSize);
    const totalBatches = Math.ceil(items.length / batchSize);
    return { batchSize, totalBatches };
  }

  /**
   * Update batch progress information
   */
  updateBatchProgress(
    progress: SyncProgress, 
    batchNumber: number, 
    totalBatches: number, 
    batch: SyncQueueItem[]
  ): void {
    progress.currentItem = `Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`;
    progress.batchInfo = {
      currentBatch: batchNumber,
      totalBatches,
      batchSize: batch.length,
      processingMode: 'parallel',
      batchItemsProcessed: 0,
      batchItemsTotal: batch.length
    };
  }

  /**
   * Update performance metrics for progress reporting
   */
  updatePerformanceMetrics(progress: SyncProgress): void {
    if (!progress.performanceMetrics) return;
    
    const now = Date.now();
    const elapsed = now - progress.performanceMetrics.startTime;
    const elapsedSeconds = elapsed / 1000;
    
    // Calculate items per second
    if (elapsedSeconds > 0) {
      progress.performanceMetrics.itemsPerSecond = progress.processedItems / elapsedSeconds;
    }
    
    // Estimate time remaining
    const remainingItems = progress.totalItems - progress.processedItems;
    if (progress.performanceMetrics.itemsPerSecond > 0) {
      progress.performanceMetrics.estimatedTimeRemaining = 
        remainingItems / progress.performanceMetrics.itemsPerSecond;
    }
  }

  /**
   * Update batch completion metrics
   */
  updateBatchCompletionMetrics(
    progress: SyncProgress, 
    batchStartTimes: number[], 
    batchStartTime: number
  ): void {
    const batchEndTime = Date.now();
    const batchDuration = batchEndTime - batchStartTime;
    
    if (progress.performanceMetrics) {
      progress.performanceMetrics.averageBatchTime = 
        batchStartTimes.length > 0 
          ? batchStartTimes.reduce((acc, _start, idx) => 
              acc + (idx < batchStartTimes.length - 1 ? 0 : batchDuration), 0) / batchStartTimes.length
          : batchDuration;
    }
  }

  /**
   * Calculate adaptive delay between batches
   */
  calculateBatchDelay(consecutiveFailures: number): number {
    return consecutiveFailures > 0 ? 500 : 200; // Longer delay after failures
  }
}

// Singleton instance
export const syncProgressTracker = new SyncProgressTracker();