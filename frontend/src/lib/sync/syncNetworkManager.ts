import type { SyncQueueItem, SyncResult } from '@/lib/db/types';

/**
 * Sync Network Manager
 * 
 * Handles HTTP requests and network operations for sync functionality.
 */
export class SyncNetworkManager {
  private readonly API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

  /**
   * Sync a single frame to the server
   */
  async syncFrame(item: SyncQueueItem, abortSignal?: AbortSignal): Promise<SyncResult> {
    // Get frame data from IndexedDB
    const { getFrameById } = await import('@/lib/db/frameOperations');
    const frame = await getFrameById(item.objectId);

    if (!frame) {
      return {
        success: false,
        message: 'Frame not found in local database',
        objectId: item.objectId,
        objectType: 'frame'
      };
    }

    // Get auth tokens
    const tokens = this.getAuthTokens();
    if (!tokens) {
      return {
        success: false,
        message: 'Not authenticated',
        objectId: item.objectId,
        objectType: 'frame'
      };
    }

    // Prepare sync request
    const syncRequest = {
      operation_type: item.operationType,
      object_type: 'frame',
      object_id: item.objectId,
      frame_data: {
        frameId: frame.frameId,
        modelNumber: frame.modelNumber,
        machineSerialNumber: frame.machineSerialNumber,
        inspectorName: frame.inspectorName,
        creationTimestamp: frame.creationTimestamp,
        lastModifiedTimestamp: frame.lastModifiedTimestamp,
        status: frame.status,
        captureCount: frame.captureCount,
        metadata: frame.metadata
      }
    };

    // Send to server
    const response = await fetch(`${this.API_BASE}/api/v1/sync/frame`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${tokens.access_token}`
      },
      body: JSON.stringify(syncRequest),
      signal: abortSignal
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP ${response.status}: ${error}`);
    }

    const result = await response.json();
    
    // Update local sync status if successful
    if (result.success) {
      const { updateFrame } = await import('@/lib/db/frameOperations');
      await updateFrame(item.objectId, { 
        syncStatus: 'synced', 
        lastSyncedAt: Date.now() 
      });
    }
    
    return {
      success: result.success,
      message: result.message,
      objectId: result.object_id,
      objectType: 'frame',
      serverObjectId: result.server_object_id
    };
  }

  /**
   * Sync a single capture to the server
   */
  async syncCapture(item: SyncQueueItem, abortSignal?: AbortSignal): Promise<SyncResult> {
    // Get capture data from IndexedDB
    const { getCaptureById } = await import('@/lib/db/captureOperations');
    const capture = await getCaptureById(item.objectId);

    if (!capture) {
      return {
        success: false,
        message: 'Capture not found in local database',
        objectId: item.objectId,
        objectType: 'capture'
      };
    }

    // Get auth tokens
    const tokens = this.getAuthTokens();
    if (!tokens) {
      return {
        success: false,
        message: 'Not authenticated',
        objectId: item.objectId,
        objectType: 'capture'
      };
    }

    // Use FormData for multipart upload to handle images
    const formData = new FormData();
    formData.append('operation_type', item.operationType);
    formData.append('object_type', 'capture');
    formData.append('object_id', item.objectId);
    formData.append('frame_id', capture.frameId);
    
    // Add capture metadata as JSON
    const captureData = {
      captureId: capture.captureId,
      frameId: capture.frameId,
      captureTimestamp: capture.captureTimestamp,
      detectionResults: capture.detectionResults
    };
    formData.append('capture_data', JSON.stringify(captureData));

    // Add image blobs if they exist
    if (capture.originalImageBlob) {
      formData.append('original_image', capture.originalImageBlob, 'original.jpg');
    }
    if (capture.processedImageBlob) {
      formData.append('processed_image', capture.processedImageBlob, 'processed.jpg');
    }
    if (capture.thumbnailBlob) {
      formData.append('thumbnail_image', capture.thumbnailBlob, 'thumbnail.jpg');
    }

    // Send to server with FormData (don't set Content-Type header)
    const response = await fetch(`${this.API_BASE}/api/v1/sync/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`
        // Content-Type will be set automatically for FormData with boundary
      },
      body: formData,
      signal: abortSignal
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP ${response.status}: ${error}`);
    }

    const result = await response.json();
    
    // Update local sync status if successful
    if (result.success) {
      const { updateCapture } = await import('@/lib/db/captureOperations');
      await updateCapture(item.objectId, { syncStatus: 'synced' });
    }
    
    return {
      success: result.success,
      message: result.message,
      objectId: result.object_id,
      objectType: 'capture',
      serverObjectId: result.server_object_id
    };
  }

  /**
   * Get authentication tokens from storage
   */
  private getAuthTokens(): { access_token: string } | null {
    try {
      const stored = localStorage.getItem('auth_tokens');
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }
}

// Singleton instance
export const syncNetworkManager = new SyncNetworkManager();