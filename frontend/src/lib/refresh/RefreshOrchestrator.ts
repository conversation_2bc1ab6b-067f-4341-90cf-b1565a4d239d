// Centralized refresh coordination system to prevent conflicts and optimize performance

import {
  RefreshRequest,
  RefreshConfig,
  RefreshBatch,
  RefreshStats,
  RefreshError,
  DEFAULT_REFRESH_CONFIGS,
  PRIORITY_WEIGHTS
} from './types';
import { syncStateManager, type SyncState } from '@/lib/sync/syncStateManager';

class RefreshOrchestrator {
  private static instance: RefreshOrchestrator | null = null;
  
  // Core coordination state
  private pendingOperations = new Map<string, Promise<void>>();
  private batchQueue = new Map<string, RefreshBatch>();
  private lastExecution = new Map<string, number>();
  private activeRequests = new Map<string, RefreshRequest>();
  
  // Performance tracking
  private stats: RefreshStats = {
    totalRequests: 0,
    completedRequests: 0,
    failedRequests: 0,
    averageExecutionTime: 0,
    duplicatesPreventedCount: 0,
    batchesProcessed: 0,
    conflictsResolved: 0,
    lastExecutionTime: {}
  };
  
  private errors: RefreshError[] = [];
  private executionTimes: number[] = [];
  
  // Event listeners for monitoring
  private listeners = new Map<string, ((event: unknown) => void)[]>();
  
  private constructor() {
    // Singleton pattern - prevent multiple instances
    this.setupSyncEventIntegration();
  }
  
  public static getInstance(): RefreshOrchestrator {
    if (!RefreshOrchestrator.instance) {
      RefreshOrchestrator.instance = new RefreshOrchestrator();
    }
    return RefreshOrchestrator.instance;
  }
  
  /**
   * Main entry point for requesting a refresh operation
   */
  public async requestRefresh(
    key: string,
    callback: () => Promise<void>,
    options: Partial<RefreshRequest> = {}
  ): Promise<void> {
    const config = this.getConfigForKey(key);
    const now = Date.now();
    
    const request: RefreshRequest = {
      id: `${key}-${now}-${Math.random().toString(36).substr(2, 9)}`,
      key,
      callback,
      priority: options.priority || config.priority,
      trigger: options.trigger || 'system',
      debounceMs: options.debounceMs || config.debounceMs,
      cooldownMs: options.cooldownMs || config.cooldownMs,
      maxRetries: options.maxRetries || config.maxRetries,
      context: options.context || {},
      createdAt: now,
      ...options
    };
    
    this.stats.totalRequests++;
    this.emit('request-received', { request });
    
    // Check for duplicate/conflicting operations
    if (this.isDuplicateRequest(request)) {
      this.stats.duplicatesPreventedCount++;
      this.emit('duplicate-prevented', { request });
      return this.pendingOperations.get(key) || Promise.resolve();
    }
    
    // Check cooldown period
    if (this.isInCooldown(request)) {
      this.stats.conflictsResolved++;
      this.emit('cooldown-applied', { request });
      return this.scheduleDelayedExecution(request);
    }
    
    // Handle batching if enabled
    if (config.batchingEnabled) {
      return this.handleBatchedRequest(request, config);
    }
    
    // Execute immediately for non-batched requests
    return this.executeRequest(request);
  }
  
  /**
   * Check if a request is a duplicate of an already pending operation
   */
  private isDuplicateRequest(request: RefreshRequest): boolean {
    return this.pendingOperations.has(request.key) || this.activeRequests.has(request.key);
  }
  
  /**
   * Check if a request is within the cooldown period
   */
  private isInCooldown(request: RefreshRequest): boolean {
    const lastExecution = this.lastExecution.get(request.key);
    if (!lastExecution) return false;
    
    const timeSinceLastExecution = Date.now() - lastExecution;
    return timeSinceLastExecution < request.cooldownMs;
  }
  
  /**
   * Schedule a request to execute after cooldown period
   */
  private scheduleDelayedExecution(request: RefreshRequest): Promise<void> {
    const lastExecution = this.lastExecution.get(request.key) || 0;
    const delay = Math.max(0, request.cooldownMs - (Date.now() - lastExecution));
    
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        this.executeRequest(request).then(resolve).catch(reject);
      }, delay);
    });
  }
  
  /**
   * Handle batched requests by adding to batch queue
   */
  private handleBatchedRequest(request: RefreshRequest, config: RefreshConfig): Promise<void> {
    const batchKey = this.getBatchKey(request);
    let batch = this.batchQueue.get(batchKey);
    
    if (!batch) {
      // Create new batch
      batch = {
        key: batchKey,
        requests: [],
        scheduledAt: Date.now(),
        timeout: setTimeout(() => {
          this.processBatch(batchKey);
        }, config.batchWindowMs)
      };
      this.batchQueue.set(batchKey, batch);
    }
    
    // Add request to batch
    batch.requests.push(request);
    
    // If batch is full, process immediately
    if (batch.requests.length >= config.maxBatchSize) {
      clearTimeout(batch.timeout);
      return this.processBatch(batchKey);
    }
    
    // Return promise that resolves when batch is processed
    return new Promise((resolve, reject) => {
      request.context = {
        ...request.context,
        batchResolve: resolve,
        batchReject: reject
      };
    });
  }
  
  /**
   * Process a batch of requests
   */
  private async processBatch(batchKey: string): Promise<void> {
    const batch = this.batchQueue.get(batchKey);
    if (!batch) return;
    
    this.batchQueue.delete(batchKey);
    this.stats.batchesProcessed++;
    
    // Sort requests by priority
    const sortedRequests = batch.requests.sort((a, b) => {
      return PRIORITY_WEIGHTS[b.priority] - PRIORITY_WEIGHTS[a.priority];
    });
    
    this.emit('batch-processing', { batch: sortedRequests });
    
    // Execute the highest priority request (others are considered duplicates)
    const primaryRequest = sortedRequests[0];
    const duplicateRequests = sortedRequests.slice(1);
    
    try {
      await this.executeRequest(primaryRequest);
      
      // Resolve all requests in the batch
      sortedRequests.forEach(request => {
        if (request.context?.batchResolve) {
          request.context.batchResolve();
        }
      });
      
      this.stats.duplicatesPreventedCount += duplicateRequests.length;
    } catch (error) {
      // Reject all requests in the batch
      sortedRequests.forEach(request => {
        if (request.context?.batchReject) {
          request.context.batchReject(error);
        }
      });
      throw error;
    }
  }
  
  /**
   * Execute a single refresh request
   */
  private async executeRequest(request: RefreshRequest): Promise<void> {
    const startTime = Date.now();
    this.activeRequests.set(request.key, request);
    
    const operation = this.performRefreshWithRetry(request);
    this.pendingOperations.set(request.key, operation);
    
    this.emit('execution-started', { request });
    
    try {
      await operation;
      
      const executionTime = Date.now() - startTime;
      this.recordSuccessfulExecution(request, executionTime);
      this.emit('execution-completed', { request, executionTime });
      
    } catch (error) {
      this.recordFailedExecution(request, error as Error);
      this.emit('execution-failed', { request, error });
      throw error;
      
    } finally {
      this.pendingOperations.delete(request.key);
      this.activeRequests.delete(request.key);
      this.lastExecution.set(request.key, Date.now());
    }
  }
  
  /**
   * Perform refresh operation with retry logic
   */
  private async performRefreshWithRetry(request: RefreshRequest): Promise<void> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= request.maxRetries; attempt++) {
      try {
        await request.callback();
        return; // Success
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < request.maxRetries) {
          // Wait before retry with exponential backoff
          const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
          this.emit('execution-retry', { request, attempt: attempt + 1, error });
        }
      }
    }
    
    // All retries failed
    throw lastError;
  }
  
  /**
   * Record successful execution metrics
   */
  private recordSuccessfulExecution(request: RefreshRequest, executionTime: number): void {
    this.stats.completedRequests++;
    this.executionTimes.push(executionTime);
    
    // Update rolling average (keep last 100 executions)
    if (this.executionTimes.length > 100) {
      this.executionTimes.shift();
    }
    
    this.stats.averageExecutionTime = 
      this.executionTimes.reduce((sum, time) => sum + time, 0) / this.executionTimes.length;
    
    this.stats.lastExecutionTime[request.key] = Date.now();
  }
  
  /**
   * Record failed execution metrics
   */
  private recordFailedExecution(request: RefreshRequest, error: Error): void {
    this.stats.failedRequests++;
    
    const refreshError: RefreshError = {
      requestId: request.id,
      key: request.key,
      error,
      retryCount: request.maxRetries,
      timestamp: Date.now(),
      recoverable: this.isRecoverableError(error)
    };
    
    this.errors.push(refreshError);
    
    // Keep only last 50 errors
    if (this.errors.length > 50) {
      this.errors.shift();
    }
  }
  
  /**
   * Determine if an error is recoverable
   */
  private isRecoverableError(error: Error): boolean {
    const recoverableMessages = [
      'network',
      'timeout',
      'fetch',
      'connection',
      'temporary'
    ];
    
    const errorMessage = error.message.toLowerCase();
    return recoverableMessages.some(msg => errorMessage.includes(msg));
  }
  
  /**
   * Get configuration for a specific key
   */
  private getConfigForKey(key: string): RefreshConfig {
    // Extract operation type from key (e.g., 'load-captures-frame-123' -> 'load-captures')
    const operationType = key.split('-')[0] + '-' + key.split('-')[1];
    return DEFAULT_REFRESH_CONFIGS[operationType] || DEFAULT_REFRESH_CONFIGS['load-captures'];
  }
  
  /**
   * Generate batch key for grouping similar requests
   */
  private getBatchKey(request: RefreshRequest): string {
    // Group by operation type and priority
    const operationType = request.key.split('-').slice(0, 2).join('-');
    return `${operationType}-${request.priority}`;
  }
  
  /**
   * Get current orchestrator statistics
   */
  public getStats(): RefreshStats {
    return { ...this.stats };
  }
  
  /**
   * Get recent errors
   */
  public getRecentErrors(): RefreshError[] {
    return [...this.errors];
  }
  
  /**
   * Clear all pending operations (useful for cleanup)
   */
  public clearPendingOperations(): void {
    this.pendingOperations.clear();
    this.activeRequests.clear();
    
    // Clear batch timeouts
    this.batchQueue.forEach(batch => {
      clearTimeout(batch.timeout);
    });
    this.batchQueue.clear();
  }
  
  /**
   * Add event listener for orchestrator events
   */
  public addEventListener(event: string, listener: (data: unknown) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }
  
  /**
   * Remove event listener
   */
  public removeEventListener(event: string, listener: (data: unknown) => void): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }
  
  /**
   * Emit event to listeners
   */
  public emit(event: string, data: unknown): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.warn(`Error in refresh orchestrator event listener for ${event}:`, error);
        }
      });
    }
  }
  
  /**
   * Setup integration with the sync event system
   */
  private setupSyncEventIntegration(): void {
    // Listen for sync state changes and emit sync-completed events when sync finishes
    const handleSyncStateChange = (syncState: SyncState) => {
      // Only emit sync-completed when sync processing stops (isProcessing changes from true to false)
      if (!syncState.isProcessing && this.lastSyncProcessingState === true) {
        const now = Date.now();
        
        // Apply cooldown to prevent rapid-fire sync-completed events
        if (now - this.lastSyncCompletedEmission >= this.SYNC_COMPLETED_COOLDOWN) {
          this.lastSyncCompletedEmission = now;
          this.emit('sync-completed', {
            timestamp: now,
            source: 'sync-state-manager',
            stats: syncState.stats
          });
        } else {
          // Log filtered event for debugging
          console.debug('Sync-completed event filtered due to cooldown', {
            timeSinceLastEmission: now - this.lastSyncCompletedEmission,
            cooldownPeriod: this.SYNC_COMPLETED_COOLDOWN
          });
        }
      }
      this.lastSyncProcessingState = syncState.isProcessing;
    };
    
    // Integrate with sync state manager (reactive approach)
    syncStateManager.getSyncState().subscribe(handleSyncStateChange);
  }

  // Track last sync processing state to detect completion
  private lastSyncProcessingState = false;
  
  // Cooldown tracking for sync-completed events to prevent loops
  private lastSyncCompletedEmission = 0;
  private readonly SYNC_COMPLETED_COOLDOWN = 2000; // 2 seconds cooldown between sync-completed events
}

// Export singleton instance
export const refreshOrchestrator = RefreshOrchestrator.getInstance();

// Integration function to manually trigger sync-completed events
export function notifySyncCompleted(): void {
  refreshOrchestrator.emit('sync-completed', {
    timestamp: Date.now(),
    source: 'manual'
  });
}