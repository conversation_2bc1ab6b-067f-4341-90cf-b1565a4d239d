// Types and interfaces for the centralized refresh coordination system

export type RefreshPriority = 'critical' | 'high' | 'medium' | 'low';

export type RefreshTrigger = 
  | 'user-action'      // User clicked refresh, created capture, etc.
  | 'sync-complete'    // Background sync operation completed
  | 'window-focus'     // User returned to tab/window
  | 'cross-tab'        // Update from another browser tab
  | 'periodic'         // Scheduled/periodic refresh
  | 'system';          // System-initiated refresh

export interface RefreshRequest {
  id: string;                    // Unique identifier for deduplication
  key: string;                   // Operation key (e.g., 'load-captures-frame-123')
  callback: () => Promise<void>; // The actual refresh operation
  priority: RefreshPriority;     // Priority level for queue ordering
  trigger: RefreshTrigger;       // What triggered this refresh
  debounceMs: number;           // Debounce time for this specific request
  cooldownMs: number;           // Minimum time between executions of this key
  maxRetries: number;           // Maximum retry attempts on failure
  context?: Record<string, unknown>; // Additional context data
  createdAt: number;            // Timestamp when request was created
}

export interface RefreshConfig {
  priority: RefreshPriority;
  debounceMs: number;
  cooldownMs: number;
  maxRetries: number;
  batchingEnabled: boolean;
  maxBatchSize: number;
  batchWindowMs: number;
}

export interface RefreshBatch {
  key: string;
  requests: RefreshRequest[];
  scheduledAt: number;
  timeout: NodeJS.Timeout;
}

export interface RefreshStats {
  totalRequests: number;
  completedRequests: number;
  failedRequests: number;
  averageExecutionTime: number;
  duplicatesPreventedCount: number;
  batchesProcessed: number;
  conflictsResolved: number;
  lastExecutionTime: Record<string, number>;
}

export interface RefreshError {
  requestId: string;
  key: string;
  error: Error;
  retryCount: number;
  timestamp: number;
  recoverable: boolean;
}

// Default configurations for different types of refresh operations
export const DEFAULT_REFRESH_CONFIGS: Record<string, RefreshConfig> = {
  'load-captures': {
    priority: 'high',
    debounceMs: 100,
    cooldownMs: 200,
    maxRetries: 3,
    batchingEnabled: true,
    maxBatchSize: 5,
    batchWindowMs: 50
  },
  'sync-status': {
    priority: 'medium',
    debounceMs: 300,
    cooldownMs: 1000,
    maxRetries: 2,
    batchingEnabled: false,
    maxBatchSize: 1,
    batchWindowMs: 0
  },
  'statistics': {
    priority: 'low',
    debounceMs: 500,
    cooldownMs: 2000,
    maxRetries: 1,
    batchingEnabled: true,
    maxBatchSize: 10,
    batchWindowMs: 200
  },
  'cross-tab-sync': {
    priority: 'medium',
    debounceMs: 150,
    cooldownMs: 300,
    maxRetries: 2,
    batchingEnabled: true,
    maxBatchSize: 3,
    batchWindowMs: 100
  }
};

// Priority weights for queue ordering (higher number = higher priority)
export const PRIORITY_WEIGHTS: Record<RefreshPriority, number> = {
  'critical': 1000,
  'high': 100,
  'medium': 10,
  'low': 1
};