// src/lib/db/sessionQueries.ts
import { Frame } from './types';
import { getDB } from './index';

export interface SessionSearchOptions {
  searchTerm?: string;
  status?: 'all' | 'active' | 'completed' | 'archived';
  inspectorName?: string;
  machineSerialNumber?: string;
  modelNumber?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'newest' | 'oldest' | 'captures' | 'modified';
  limit?: number;
  offset?: number;
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  completedSessions: number;
  archivedSessions: number;
  totalCaptures: number;
  recentActivity: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  topInspectors: {
    name: string;
    sessionCount: number;
    captureCount: number;
  }[];
  topMachines: {
    serialNumber: string;
    sessionCount: number;
    captureCount: number;
  }[];
}

// Advanced search for sessions
export async function searchSessions(options: SessionSearchOptions = {}): Promise<Frame[]> {
  const db = await getDB();
  let frames = await db.getAll('frames');

  // Apply filters
  if (options.status && options.status !== 'all') {
    frames = frames.filter(frame => frame.status === options.status);
  }

  if (options.inspectorName) {
    frames = frames.filter(frame => 
      frame.inspectorName.toLowerCase().includes(options.inspectorName!.toLowerCase())
    );
  }

  if (options.machineSerialNumber) {
    frames = frames.filter(frame => 
      frame.machineSerialNumber.toLowerCase().includes(options.machineSerialNumber!.toLowerCase())
    );
  }

  if (options.modelNumber) {
    frames = frames.filter(frame => 
      frame.modelNumber.toLowerCase().includes(options.modelNumber!.toLowerCase())
    );
  }

  if (options.searchTerm) {
    const search = options.searchTerm.toLowerCase();
    frames = frames.filter(frame => 
      frame.modelNumber.toLowerCase().includes(search) ||
      frame.machineSerialNumber.toLowerCase().includes(search) ||
      frame.inspectorName.toLowerCase().includes(search)
    );
  }

  if (options.dateRange) {
    const startTime = options.dateRange.start.getTime();
    const endTime = options.dateRange.end.getTime();
    frames = frames.filter(frame => 
      frame.creationTimestamp >= startTime && frame.creationTimestamp <= endTime
    );
  }

  // Apply sorting
  frames.sort((a, b) => {
    switch (options.sortBy) {
      case 'oldest':
        return a.creationTimestamp - b.creationTimestamp;
      case 'captures':
        return b.captureCount - a.captureCount;
      case 'modified':
        return b.lastModifiedTimestamp - a.lastModifiedTimestamp;
      case 'newest':
      default:
        return b.creationTimestamp - a.creationTimestamp;
    }
  });

  // Apply pagination
  if (options.offset || options.limit) {
    const start = options.offset || 0;
    const end = options.limit ? start + options.limit : undefined;
    frames = frames.slice(start, end);
  }

  return frames;
}

// Get sessions by inspector
export async function getSessionsByInspector(inspectorName: string): Promise<Frame[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  return frames.filter(frame => frame.inspectorName === inspectorName)
    .sort((a, b) => b.creationTimestamp - a.creationTimestamp);
}

// Get sessions by machine
export async function getSessionsByMachine(machineSerialNumber: string): Promise<Frame[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  return frames.filter(frame => frame.machineSerialNumber === machineSerialNumber)
    .sort((a, b) => b.creationTimestamp - a.creationTimestamp);
}

// Get sessions by model
export async function getSessionsByModel(modelNumber: string): Promise<Frame[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  return frames.filter(frame => frame.modelNumber === modelNumber)
    .sort((a, b) => b.creationTimestamp - a.creationTimestamp);
}

// Get recent sessions (within last N days)
export async function getRecentSessions(days: number = 7): Promise<Frame[]> {
  const db = await getDB();
  const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
  const frames = await db.getAll('frames');
  
  return frames.filter(frame => frame.creationTimestamp >= cutoffTime)
    .sort((a, b) => b.creationTimestamp - a.creationTimestamp);
}

// Get comprehensive session statistics
export async function getSessionStats(): Promise<SessionStats> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  
  // Calculate time ranges for activity statistics
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const todayTime = today.getTime();
  
  const thisWeek = new Date();
  thisWeek.setDate(thisWeek.getDate() - 7);
  thisWeek.setHours(0, 0, 0, 0);
  const thisWeekTime = thisWeek.getTime();
  
  const thisMonth = new Date();
  thisMonth.setDate(thisMonth.getDate() - 30);
  thisMonth.setHours(0, 0, 0, 0);
  const thisMonthTime = thisMonth.getTime();

  // Basic counts
  const totalSessions = frames.length;
  const activeSessions = frames.filter(f => f.status === 'active').length;
  const completedSessions = frames.filter(f => f.status === 'completed').length;
  const archivedSessions = frames.filter(f => f.status === 'archived').length;
  const totalCaptures = frames.reduce((sum, frame) => sum + frame.captureCount, 0);

  // Recent activity
  const recentActivity = {
    today: frames.filter(f => f.creationTimestamp >= todayTime).length,
    thisWeek: frames.filter(f => f.creationTimestamp >= thisWeekTime).length,
    thisMonth: frames.filter(f => f.creationTimestamp >= thisMonthTime).length
  };

  // Top inspectors
  const inspectorStats = new Map<string, { sessionCount: number; captureCount: number }>();
  frames.forEach(frame => {
    const current = inspectorStats.get(frame.inspectorName) || { sessionCount: 0, captureCount: 0 };
    current.sessionCount++;
    current.captureCount += frame.captureCount;
    inspectorStats.set(frame.inspectorName, current);
  });

  const topInspectors = Array.from(inspectorStats.entries())
    .map(([name, stats]) => ({ name, ...stats }))
    .sort((a, b) => b.sessionCount - a.sessionCount)
    .slice(0, 5);

  // Top machines
  const machineStats = new Map<string, { sessionCount: number; captureCount: number }>();
  frames.forEach(frame => {
    const current = machineStats.get(frame.machineSerialNumber) || { sessionCount: 0, captureCount: 0 };
    current.sessionCount++;
    current.captureCount += frame.captureCount;
    machineStats.set(frame.machineSerialNumber, current);
  });

  const topMachines = Array.from(machineStats.entries())
    .map(([serialNumber, stats]) => ({ serialNumber, ...stats }))
    .sort((a, b) => b.sessionCount - a.sessionCount)
    .slice(0, 5);

  return {
    totalSessions,
    activeSessions,
    completedSessions,
    archivedSessions,
    totalCaptures,
    recentActivity,
    topInspectors,
    topMachines
  };
}

// Get unique values for filtering
export async function getUniqueInspectors(): Promise<string[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  const inspectors = new Set(frames.map(frame => frame.inspectorName));
  return Array.from(inspectors).sort();
}

export async function getUniqueMachines(): Promise<string[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  const machines = new Set(frames.map(frame => frame.machineSerialNumber));
  return Array.from(machines).sort();
}

export async function getUniqueModels(): Promise<string[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  const models = new Set(frames.map(frame => frame.modelNumber));
  return Array.from(models).sort();
}

// Check if a session exists with the same parameters
export async function findSimilarSessions(
  modelNumber: string, 
  machineSerialNumber: string, 
  inspectorName: string
): Promise<Frame[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  
  return frames.filter(frame => 
    frame.modelNumber === modelNumber &&
    frame.machineSerialNumber === machineSerialNumber &&
    frame.inspectorName === inspectorName
  ).sort((a, b) => b.creationTimestamp - a.creationTimestamp);
}

// Archive old sessions
export async function archiveOldSessions(olderThanDays: number = 30): Promise<number> {
  const db = await getDB();
  const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
  const frames = await db.getAll('frames');
  
  let archivedCount = 0;
  const tx = db.transaction('frames', 'readwrite');
  
  for (const frame of frames) {
    if (frame.status === 'completed' && frame.lastModifiedTimestamp < cutoffTime) {
      frame.status = 'archived';
      frame.lastModifiedTimestamp = Date.now();
      frame.syncStatus = 'pending';
      await tx.store.put(frame);
      archivedCount++;
    }
  }
  
  await tx.done;
  return archivedCount;
}