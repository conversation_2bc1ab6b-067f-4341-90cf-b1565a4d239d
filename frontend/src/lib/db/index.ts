// src/lib/db/index.ts
import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { Frame, Capture, SyncQueueItem } from './types';

interface WeldDetectionDB extends DBSchema {
  frames: {
    key: string;
    value: Frame;
    indexes: {
      'by-machine-inspector': [string, string];
      'by-sync-status': string;
    };
  };
  captures: {
    key: string;
    value: Capture;
    indexes: {
      'by-frame': string;
      'by-sync-status': string;
    };
  };
  syncQueue: {
    key: number;
    value: SyncQueueItem;
    indexes: {
      'by-status-priority': [string, number];
      'by-object': [string, string];
    };
  };
}

// Current database version
const DB_VERSION = 2;
const DB_NAME = 'weld-detection-db';

// DB instance cache
let dbPromise: Promise<IDBPDatabase<WeldDetectionDB>> | null = null;

// Initialize the database
export async function initDB(): Promise<IDBPDatabase<WeldDetectionDB>> {
  if (!dbPromise) {
    dbPromise = openDB<WeldDetectionDB>(DB_NAME, DB_VERSION, {
      upgrade(db, oldVersion, newVersion, transaction) {
        console.log(`Upgrading database from version ${oldVersion} to ${newVersion}`);
        
        // Handle database creation and migrations
        if (oldVersion < 1) {
          // Create frames store
          const framesStore = db.createObjectStore('frames', { keyPath: 'frameId' });
          framesStore.createIndex('by-machine-inspector', ['machineSerialNumber', 'inspectorName']);
          framesStore.createIndex('by-sync-status', 'syncStatus');
          
          // Create captures store
          const capturesStore = db.createObjectStore('captures', { keyPath: 'captureId' });
          capturesStore.createIndex('by-frame', 'frameId');
          capturesStore.createIndex('by-sync-status', 'syncStatus');
          
          // Create sync queue store
          const syncQueueStore = db.createObjectStore('syncQueue', { 
            keyPath: 'queueId', 
            autoIncrement: true 
          });
          syncQueueStore.createIndex('by-status-priority', ['status', 'priority']);
        }
        
        // Add by-object index to sync queue in version 2
        if (oldVersion < 2) {
          const syncQueueStore = transaction.objectStore('syncQueue');
          syncQueueStore.createIndex('by-object', ['objectId', 'objectType']);
        }
      },
      blocked() {
        console.warn('Database opening blocked. Another tab may have the database open.');
      },
      blocking() {
        console.warn('This tab is blocking a newer version of the database.');
      },
      terminated() {
        console.error('Database connection was terminated unexpectedly.');
        dbPromise = null;
      }
    });
  }
  
  return dbPromise;
}

// Get database instance
export async function getDB(): Promise<IDBPDatabase<WeldDetectionDB>> {
  if (!dbPromise) {
    return initDB();
  }
  return dbPromise;
}

// Close database connection
export function closeDB(): void {
  if (dbPromise) {
    dbPromise.then(db => db.close());
    dbPromise = null;
  }
}

// Clear all data (useful for testing or user-initiated reset)
export async function clearDatabase(): Promise<void> {
  const db = await getDB();
  const tx = db.transaction(['frames', 'captures', 'syncQueue'], 'readwrite');
  await Promise.all([
    tx.objectStore('frames').clear(),
    tx.objectStore('captures').clear(),
    tx.objectStore('syncQueue').clear(),
    tx.done
  ]);
}