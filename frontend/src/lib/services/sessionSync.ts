// src/lib/services/sessionSync.ts
// Enhanced session sync service with session-specific logic and optimizations
import { Frame } from '@/lib/db/types';
import { syncManager, startSync, getSyncStatus, startFrameSync } from '@/lib/sync/syncManager';
import { syncStateManager } from '@/lib/sync/syncStateManager';
import { cleanupOldSyncItems } from '@/lib/db/syncQueueHelper';

export interface ApiFrame {
  frameId: string;
  modelNumber: string;
  machineSerialNumber: string;
  inspectorName: string;
  creationTimestamp: number;
  lastModifiedTimestamp: number;
  status: 'active' | 'completed' | 'archived';
  captureCount: number;
  metadata?: Record<string, unknown>;
  syncStatus: 'synced' | 'pending' | 'conflict';
  lastSyncedAt?: number;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
const DEBUG_MODE = process.env.NEXT_PUBLIC_DEBUG_MODE === 'true';

class SessionSyncService {
  private lastCleanupTime = 0;
  private readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

  private isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Sync pending items to server with automatic cleanup
   */
  async syncToServer(progressCallback?: (progress: unknown) => void): Promise<unknown> {
    if (!this.isOnline()) {
      throw new Error('Offline - cannot sync to server');
    }

    try {
      // Perform periodic cleanup of old sync items
      await this.performPeriodicCleanup();

      const result = await startSync(progressCallback);

      // Update sync state manager after successful sync
      const stats = await getSyncStatus();
      syncStateManager.updateSyncStats(stats);

      return result;
    } catch (error) {
      console.error('Error syncing to server:', error);
      throw error;
    }
  }

  /**
   * Sync specific session/frame to server
   */
  async syncSessionToServer(frameId: string, progressCallback?: (progress: unknown) => void): Promise<unknown> {
    if (!this.isOnline()) {
      throw new Error('Offline - cannot sync session to server');
    }

    try {
      const result = await startFrameSync(frameId, progressCallback);

      // Update sync state manager after successful sync
      const stats = await getSyncStatus();
      syncStateManager.updateSyncStats(stats);

      return result;
    } catch (error) {
      console.error('Error syncing session to server:', error);
      throw error;
    }
  }

  /**
   * Perform periodic cleanup of old sync items
   */
  private async performPeriodicCleanup(): Promise<void> {
    const now = Date.now();
    if (now - this.lastCleanupTime >= this.CLEANUP_INTERVAL) {
      try {
        const deletedCount = await cleanupOldSyncItems();
        if (deletedCount > 0) {
          console.log(`Cleaned up ${deletedCount} old sync items`);
        }
        this.lastCleanupTime = now;
      } catch (error) {
        console.warn('Failed to cleanup old sync items:', error);
      }
    }
  }

  /**
   * Get current sync status
   */
  async getSyncStats() {
    try {
      return await getSyncStatus();
    } catch (error) {
      console.error('Error getting sync stats:', error);
      return {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0
      };
    }
  }

  /**
   * Check if sync manager is currently processing
   */
  get isSyncing(): boolean {
    return syncManager.isSyncing;
  }

  /**
   * Stop current sync process
   */
  stopSync(): void {
    syncManager.stopSync();
  }

  /**
   * Process pending sync queue manually
   */
  async processPendingSync(progressCallback?: (progress: unknown) => void): Promise<unknown> {
    return await this.syncToServer(progressCallback);
  }

  /**
   * Process pending sync for specific session
   */
  async processPendingSessionSync(frameId: string, progressCallback?: (progress: unknown) => void): Promise<unknown> {
    return await this.syncSessionToServer(frameId, progressCallback);
  }

  /**
   * Check connectivity to server
   */
  async checkServerConnectivity(): Promise<boolean> {
    if (!this.isOnline()) {
      return false;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`${API_BASE_URL}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      // Silently handle connectivity errors - this is expected in offline-first mode
      if (error instanceof Error && error.name !== 'AbortError' && DEBUG_MODE) {
        console.debug('Server connectivity check failed (this is normal if backend is not running):', error.message);
      }
      return false;
    }
  }

  // Convert API frame to local frame format
  convertApiFrameToLocal(apiFrame: ApiFrame): Frame {
    return {
      frameId: apiFrame.frameId,
      modelNumber: apiFrame.modelNumber,
      machineSerialNumber: apiFrame.machineSerialNumber,
      inspectorName: apiFrame.inspectorName,
      creationTimestamp: apiFrame.creationTimestamp,
      lastModifiedTimestamp: apiFrame.lastModifiedTimestamp,
      status: apiFrame.status,
      captureCount: apiFrame.captureCount,
      metadata: apiFrame.metadata,
      syncStatus: 'synced', // Mark as synced since it came from server
      lastSyncedAt: Date.now()
    };
  }

  // Convert local frame to API format
  convertLocalFrameToApi(frame: Frame): ApiFrame {
    return {
      frameId: frame.frameId,
      modelNumber: frame.modelNumber,
      machineSerialNumber: frame.machineSerialNumber,
      inspectorName: frame.inspectorName,
      creationTimestamp: frame.creationTimestamp,
      lastModifiedTimestamp: frame.lastModifiedTimestamp,
      status: frame.status,
      captureCount: frame.captureCount,
      metadata: frame.metadata,
      syncStatus: frame.syncStatus,
      lastSyncedAt: frame.lastSyncedAt
    };
  }
}

// Singleton instance
export const sessionSyncService = new SessionSyncService();

// Simplified session loading - always offline-first
export async function loadSessionsWithFallback(): Promise<Frame[]> {
  try {
    // Always load from IndexedDB first (offline-first approach)
    const { getAllFrames } = await import('@/lib/db/frameOperations');
    const localFrames = await getAllFrames();

    // Start background sync without waiting for it, but only if not already syncing
    if (navigator.onLine && !sessionSyncService.isSyncing) {
      // Fire and forget - don't let this block the UI
      setTimeout(() => {
        sessionSyncService.processPendingSync().catch(() => {
          // Silently ignore sync errors in background
        });
      }, 100);
    }

    return localFrames;
  } catch (error) {
    console.error('Error loading sessions:', error);
    return [];
  }
}