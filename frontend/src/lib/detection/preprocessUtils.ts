// src/lib/detection/preprocessUtils.ts
import * as tf from '@tensorflow/tfjs';

export interface PreprocessResult {
  tensor: tf.Tensor;
  ratio: { x: number; y: number };
  originalSize: { width: number; height: number };
}

/**
 * Preprocess image for YOLOv8 model
 * Resizes image while maintaining aspect ratio and pads to square
 */
export function preprocess(
  source: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement,
  targetWidth: number = 640,
  targetHeight: number = 640
): PreprocessResult {
  // Get original dimensions
  const originalWidth = source instanceof HTMLCanvasElement ? source.width : source.videoWidth || source.width;
  const originalHeight = source instanceof HTMLCanvasElement ? source.height : source.videoHeight || source.height;

  // Calculate scaling ratio to fit image into target size while maintaining aspect ratio
  const ratio = Math.min(targetWidth / originalWidth, targetHeight / originalHeight);
  
  // Calculate new dimensions
  const newWidth = Math.round(originalWidth * ratio);
  const newHeight = Math.round(originalHeight * ratio);
  
  // Calculate padding
  const padX = (targetWidth - newWidth) / 2;
  const padY = (targetHeight - newHeight) / 2;

  // Create canvas for preprocessing
  const canvas = document.createElement('canvas');
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Could not create canvas context for preprocessing');
  }

  // Fill with black background (padding)
  ctx.fillStyle = '#000000';
  ctx.fillRect(0, 0, targetWidth, targetHeight);
  
  // Draw resized image in center
  ctx.drawImage(source, padX, padY, newWidth, newHeight);

  // Convert to tensor
  const tensor = tf.browser.fromPixels(canvas)
    .div(255.0) // Normalize to [0, 1]
    .expandDims(0) // Add batch dimension
    .cast('float32');

  return {
    tensor,
    ratio: { x: ratio, y: ratio },
    originalSize: { width: originalWidth, height: originalHeight }
  };
}

/**
 * Convert canvas to tensor without resizing (for direct inference)
 */
export function canvasToTensor(canvas: HTMLCanvasElement): tf.Tensor {
  return tf.browser.fromPixels(canvas)
    .div(255.0)
    .expandDims(0)
    .cast('float32');
}

/**
 * Resize image element to specific dimensions
 */
export function resizeImage(
  source: HTMLImageElement | HTMLVideoElement,
  width: number,
  height: number
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('Could not create canvas context for resizing');
  }
  
  ctx.drawImage(source, 0, 0, width, height);
  return canvas;
}