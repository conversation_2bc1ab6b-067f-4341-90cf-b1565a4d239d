// src/lib/detection/detector.ts
import * as tf from '@tensorflow/tfjs';
import { DetectionResult } from '@/lib/db/types';
import { preprocess, PreprocessResult } from './preprocessUtils';
import { COCO_CLASSES } from './labels';

export interface DetectionOptions {
  confidenceThreshold?: number;
  iouThreshold?: number;
  maxDetections?: number;
  inputSize?: number;
}

export interface PerformanceMetrics {
  preprocessTime: number;
  inferenceTime: number;
  postprocessTime: number;
  totalTime: number;
}

/**
 * Main detection function
 */
export async function detect(
  imageSource: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement,
  model: tf.GraphModel,
  options: DetectionOptions = {}
): Promise<{ detections: DetectionResult[]; metrics: PerformanceMetrics }> {
  const startTime = performance.now();
  
  const {
    confidenceThreshold = 0.25,
    iouThreshold = 0.45,
    maxDetections = 100,
    inputSize = 640
  } = options;

  let preprocessResult: PreprocessResult;
  let inferenceResult: tf.Tensor;
  
  try {
    // Preprocessing
    const preprocessStartTime = performance.now();
    preprocessResult = preprocess(imageSource, inputSize, inputSize);
    const preprocessEndTime = performance.now();

    // Inference
    const inferenceStartTime = performance.now();
    inferenceResult = model.predict(preprocessResult.tensor) as tf.Tensor;
    const inferenceEndTime = performance.now();

    // Post-processing
    const postprocessStartTime = performance.now();
    const detections = await postprocess(
      inferenceResult,
      preprocessResult,
      confidenceThreshold,
      iouThreshold,
      maxDetections
    );
    const postprocessEndTime = performance.now();

    const totalTime = performance.now() - startTime;

    const metrics: PerformanceMetrics = {
      preprocessTime: preprocessEndTime - preprocessStartTime,
      inferenceTime: inferenceEndTime - inferenceStartTime,
      postprocessTime: postprocessEndTime - postprocessStartTime,
      totalTime
    };

    return { detections, metrics };

  } finally {
    // Clean up tensors
    preprocessResult?.tensor.dispose();
    inferenceResult?.dispose();
  }
}

/**
 * Post-process model output to extract detections
 */
async function postprocess(
  output: tf.Tensor,
  preprocessResult: PreprocessResult,
  confidenceThreshold: number,
  iouThreshold: number,
  maxDetections: number
): Promise<DetectionResult[]> {
  // YOLOv8 output format: [batch, 84, 8400] where 84 = 4 bbox coords + 80 class scores
  const [, numFeatures, numBoxes] = output.shape;
  
  if (numFeatures !== 84) {
    throw new Error(`Expected 84 features, got ${numFeatures}. Make sure you're using a YOLOv8 model.`);
  }

  // Transpose to [batch, 8400, 84] for easier processing
  const transposed = output.transpose([0, 2, 1]);
  const outputData = await transposed.data();
  
  const detections: DetectionResult[] = [];
  const numClasses = 80; // COCO dataset classes

  // Process each detection
  for (let i = 0; i < numBoxes; i++) {
    const baseIndex = i * numFeatures;
    
    // Extract bbox coordinates (center format)
    const centerX = outputData[baseIndex];
    const centerY = outputData[baseIndex + 1];
    const width = outputData[baseIndex + 2];
    const height = outputData[baseIndex + 3];
    
    // Find best class
    let maxClassScore = 0;
    let bestClassIndex = 0;
    
    for (let j = 0; j < numClasses; j++) {
      const score = outputData[baseIndex + 4 + j];
      if (score > maxClassScore) {
        maxClassScore = score;
        bestClassIndex = j;
      }
    }
    
    // Check confidence threshold
    if (maxClassScore < confidenceThreshold) {
      continue;
    }
    
    // Convert from center format to corner format and scale back to original image
    const { ratio, originalSize } = preprocessResult;
    
    // Scale coordinates back to input image size (640x640)
    const x1 = centerX - width / 2;
    const y1 = centerY - height / 2;
    const x2 = centerX + width / 2;
    const y2 = centerY + height / 2;
    
    // Scale to original image size
    const originalX1 = x1 / ratio.x;
    const originalY1 = y1 / ratio.y;
    const originalX2 = x2 / ratio.x;
    const originalY2 = y2 / ratio.y;
    
    // Clamp to image bounds
    const clampedX1 = Math.max(0, Math.min(originalSize.width, originalX1));
    const clampedY1 = Math.max(0, Math.min(originalSize.height, originalY1));
    const clampedX2 = Math.max(0, Math.min(originalSize.width, originalX2));
    const clampedY2 = Math.max(0, Math.min(originalSize.height, originalY2));
    
    detections.push({
      id: crypto.randomUUID(),
      class: COCO_CLASSES[bestClassIndex] || `Class ${bestClassIndex}`,
      confidence: maxClassScore,
      bbox: {
        x1: clampedX1,
        y1: clampedY1,
        x2: clampedX2,
        y2: clampedY2
      }
    });
  }
  
  // Clean up
  transposed.dispose();
  
  // Apply Non-Maximum Suppression (NMS)
  const filteredDetections = await applyNMS(detections, iouThreshold, maxDetections);
  
  return filteredDetections;
}

/**
 * Apply Non-Maximum Suppression to remove overlapping detections
 */
async function applyNMS(
  detections: DetectionResult[],
  iouThreshold: number,
  maxDetections: number
): Promise<DetectionResult[]> {
  if (detections.length === 0) {
    return [];
  }
  
  // Sort by confidence (highest first)
  const sortedDetections = [...detections].sort((a, b) => b.confidence - a.confidence);
  
  const selectedDetections: DetectionResult[] = [];
  const suppressed = new Set<number>();
  
  for (let i = 0; i < sortedDetections.length && selectedDetections.length < maxDetections; i++) {
    if (suppressed.has(i)) {
      continue;
    }
    
    const currentDetection = sortedDetections[i];
    selectedDetections.push(currentDetection);
    
    // Suppress overlapping detections
    for (let j = i + 1; j < sortedDetections.length; j++) {
      if (suppressed.has(j)) {
        continue;
      }
      
      const otherDetection = sortedDetections[j];
      
      // Only compare detections of the same class
      if (currentDetection.class !== otherDetection.class) {
        continue;
      }
      
      const iou = calculateIOU(currentDetection.bbox, otherDetection.bbox);
      
      if (iou > iouThreshold) {
        suppressed.add(j);
      }
    }
  }
  
  return selectedDetections;
}

/**
 * Calculate Intersection over Union (IoU) for two bounding boxes
 */
function calculateIOU(
  bbox1: { x1: number; y1: number; x2: number; y2: number },
  bbox2: { x1: number; y1: number; x2: number; y2: number }
): number {
  // Calculate intersection area
  const intersectionX1 = Math.max(bbox1.x1, bbox2.x1);
  const intersectionY1 = Math.max(bbox1.y1, bbox2.y1);
  const intersectionX2 = Math.min(bbox1.x2, bbox2.x2);
  const intersectionY2 = Math.min(bbox1.y2, bbox2.y2);
  
  const intersectionWidth = Math.max(0, intersectionX2 - intersectionX1);
  const intersectionHeight = Math.max(0, intersectionY2 - intersectionY1);
  const intersectionArea = intersectionWidth * intersectionHeight;
  
  // Calculate areas of both bounding boxes
  const bbox1Area = (bbox1.x2 - bbox1.x1) * (bbox1.y2 - bbox1.y1);
  const bbox2Area = (bbox2.x2 - bbox2.x1) * (bbox2.y2 - bbox2.y1);
  
  // Calculate union area
  const unionArea = bbox1Area + bbox2Area - intersectionArea;
  
  // Return IoU
  return unionArea > 0 ? intersectionArea / unionArea : 0;
}

/**
 * Validate model output shape
 */
export function validateModelOutput(output: tf.Tensor): boolean {
  const shape = output.shape;
  
  // Expected shape for YOLOv8: [batch, 84, anchors]
  if (shape.length !== 3) {
    console.error(`Invalid output shape: expected 3 dimensions, got ${shape.length}`);
    return false;
  }
  
  if (shape[1] !== 84) {
    console.error(`Invalid output shape: expected 84 features, got ${shape[1]}`);
    return false;
  }
  
  return true;
}