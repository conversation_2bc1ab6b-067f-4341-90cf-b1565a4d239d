'use client';

import { useEffect, useCallback } from 'react';
import { useDebounce } from './useDebounce';

/**
 * Custom hook for auto-refreshing data based on window focus and visibility changes
 * @param refreshCallback - Function to call when refresh is needed
 * @param options - Configuration options
 */
export function useAutoRefresh(
  refreshCallback: () => void,
  options: {
    debounceMs?: number;
    enabled?: boolean;
  } = {}
) {
  const { debounceMs = 300, enabled = true } = options;
  
  // Debounce the refresh callback to prevent excessive calls
  const debouncedRefresh = useDebounce(refreshCallback, debounceMs);
  
  // Stable callback that only updates when dependencies change
  const stableRefresh = useCallback(() => {
    if (enabled) {
      debouncedRefresh();
    }
  }, [debouncedRefresh, enabled]);
  
  useEffect(() => {
    if (!enabled) return;
    
    // Handle window focus events
    const handleFocus = () => {
      stableRefresh();
    };
    
    // Handle visibility change events
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        stableRefresh();
      }
    };
    
    // Add event listeners
    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Cleanup function
    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [stableRefresh, enabled]);
}