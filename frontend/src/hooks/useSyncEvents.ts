'use client';

import { useEffect, useCallback, useRef } from 'react';
import { syncStateManager } from '@/lib/sync/syncStateManager';
import type { SyncState, SyncStats, SyncProgress } from '@/lib/db/types';

/**
 * Custom hook to listen for sync completion events
 * Triggers callback when sync operations complete (isProcessing changes from true to false)
 * @param callback Function to call when sync completes
 */
export function useSyncEvents(callback: () => void) {
  const stableCallback = useCallback(callback, [callback]);
  const previousProcessingRef = useRef<boolean>(false);
  
  useEffect(() => {
    const subscription = syncStateManager.getProcessingStatus().subscribe(isProcessing => {
      // Trigger callback when processing changes from true to false (sync completion)
      if (previousProcessingRef.current && !isProcessing) {
        // Add small delay to prevent feedback loops
        setTimeout(stableCallback, 2000); // 2-second cooldown as mentioned in CLAUDE.md
      }
      previousProcessingRef.current = isProcessing;
    });
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to reactive sync state updates
 * @param callback Function to call when sync state changes
 */
export function useSyncState(callback: (state: SyncState) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = syncStateManager.getSyncState().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync stats only
 * @param callback Function to call when sync stats change
 */
export function useSyncStats(callback: (stats: SyncStats) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = syncStateManager.getSyncStats().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync progress updates
 * @param callback Function to call when sync progress changes
 */
export function useSyncProgress(callback: (progress: SyncProgress | null) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = syncStateManager.getSyncProgress().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}