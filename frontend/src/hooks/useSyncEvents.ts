'use client';

import { useEffect, useCallback } from 'react';
import { syncEventEmitter } from '@/lib/sync/syncManager';
import { syncStateManager } from '@/lib/sync/syncStateManager';
import type { SyncState } from '@/lib/sync/syncStateManager';

/**
 * Custom hook to listen for sync completion events
 * @param callback Function to call when sync completes
 */
export function useSyncEvents(callback: () => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    syncEventEmitter.addEventListener(stableCallback);
    
    return () => {
      syncEventEmitter.removeEventListener(stableCallback);
    };
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to reactive sync state updates
 * @param callback Function to call when sync state changes
 */
export function useSyncState(callback: (state: SyncState) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = syncStateManager.getSyncState().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync stats only
 * @param callback Function to call when sync stats change
 */
export function useSyncStats(callback: (stats: { pending: number; processing: number; completed: number; failed: number; }) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = syncStateManager.getSyncStats().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync progress updates
 * @param callback Function to call when sync progress changes
 */
export function useSyncProgress(callback: (progress: { totalItems: number; processedItems: number; successfulItems: number; failedItems: number; currentItem?: string; isProcessing: boolean; } | null) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = syncStateManager.getSyncProgress().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}