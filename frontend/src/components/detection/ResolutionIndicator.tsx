// src/components/detection/ResolutionIndicator.tsx
interface Resolution {
    width: number;
    height: number;
  }
  
  interface ResolutionIndicatorProps {
    original: Resolution;
    target: Resolution;
  }
  
  export default function ResolutionIndicator({ original, target }: ResolutionIndicatorProps) {
    return (
      <div className="absolute top-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
        <div>Camera: {original.width}×{original.height}</div>
        <div>Target: {target.width}×{target.height}</div>
      </div>
    );
  }