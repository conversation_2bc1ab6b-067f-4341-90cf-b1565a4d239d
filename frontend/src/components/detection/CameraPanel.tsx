// src/components/detection/CameraPanel.tsx
'use client';

import { useRef, useState, useEffect } from 'react';
import CameraControls from '@/components/detection/CameraControls';
import ResolutionIndicator from '@/components/detection/ResolutionIndicator';
import { useSession } from '@/context/SessionContext';
import { createCapture } from '@/lib/db/captureOperations';
import { DetectionResult } from '@/lib/db/types';
import { modelService, detect, PerformanceMetrics, LoadingProgress, createDetectionCanvas } from '@/lib/detection';
import { useOptionalCaptureUpdates } from '@/hooks/useOptionalCaptureUpdates';
import { broadcastRefreshTrigger } from '@/hooks/useOrchestratedRefresh';
import * as tf from '@tensorflow/tfjs';

interface CameraPanelProps {
  // Optional prop for backward compatibility, but context is preferred
  onCaptureCreated?: () => void;
}

export default function CameraPanel({ onCaptureCreated }: CameraPanelProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [resolution, setResolution] = useState({ width: 0, height: 0 });
  const [isCapturing, setIsCapturing] = useState(false);
  const { frameId } = useSession();
  
  // Get capture updates context (optional - will be null if not wrapped in provider)
  const captureUpdates = useOptionalCaptureUpdates();
  
  // AI Detection state
  const [model, setModel] = useState<tf.GraphModel | null>(null);
  const [modelLoading, setModelLoading] = useState(false);
  const [modelProgress, setModelProgress] = useState(0);
  const [modelError, setModelError] = useState<string | null>(null);
  const [performance, setPerformance] = useState<PerformanceMetrics | null>(null);
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);
  
  // Function to start camera
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: { ideal: 1280 }, height: { ideal: 720 } }
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          setCameraActive(true);
          if (videoRef.current) {
            setResolution({
              width: videoRef.current.videoWidth,
              height: videoRef.current.videoHeight
            });
          }
        };
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
    }
  };
  
  // Function to stop camera
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      const tracks = stream.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setCameraActive(false);
    }
  };

  // Helper function to convert canvas to blob
  const canvasToBlob = (canvas: HTMLCanvasElement, quality: number = 0.9): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert canvas to blob'));
          }
        },
        'image/jpeg',
        quality
      );
    });
  };

  // Helper function to resize image to 640x640
  const resizeImageTo640 = (sourceCanvas: HTMLCanvasElement): HTMLCanvasElement => {
    const resizeCanvas = document.createElement('canvas');
    const resizeCtx = resizeCanvas.getContext('2d');
    
    if (!resizeCtx) {
      throw new Error('Could not create resize canvas context');
    }
    
    resizeCanvas.width = 640;
    resizeCanvas.height = 640;
    
    // Draw the source canvas resized to 640x640
    resizeCtx.drawImage(sourceCanvas, 0, 0, 640, 640);
    
    return resizeCanvas;
  };

  // Real AI detection function using TensorFlow.js
  const performDetection = async (canvas: HTMLCanvasElement): Promise<{ detections: DetectionResult[]; metrics: PerformanceMetrics }> => {
    if (!model) {
      throw new Error('AI model not loaded');
    }
    
    console.log('Performing detection with TensorFlow.js...');
    
    // Use the real detection engine
    const result = await detect(canvas, model, {
      confidenceThreshold: 0.25,
      iouThreshold: 0.45,
      maxDetections: 100,
      inputSize: 640
    });
    
    console.log(`Detection completed: found ${result.detections.length} objects`);
    console.log('Performance:', result.metrics);
    
    return result;
  };

  
  // Function to capture image with complete pipeline
  const captureImage = async () => {
    if (!videoRef.current || !canvasRef.current || !cameraActive || !frameId || isCapturing || !model) {
      if (!model) {
        alert('AI model is not loaded yet. Please wait for the model to load.');
      }
      return;
    }

    setIsCapturing(true);
    
    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      // Step 1: Capture and crop the image to square
      const videoWidth = video.videoWidth;
      const videoHeight = video.videoHeight;
      
      // Determine the square size and offset for center cropping
      const size = Math.min(videoWidth, videoHeight);
      const xOffset = (videoWidth - size) / 2;
      const yOffset = (videoHeight - size) / 2;
      
      // Set canvas size to the crop size
      canvas.width = size;
      canvas.height = size;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Could not get canvas context');
      }

      // Draw the center-cropped image to the canvas
      ctx.drawImage(
        video, 
        xOffset, yOffset, size, size, // Source rectangle
        0, 0, size, size              // Destination rectangle
      );
      
      // Step 2: Create resized version (640x640) for detection
      const resizedCanvas = resizeImageTo640(canvas);
      const originalBlob = await canvasToBlob(canvas);
      
      // Step 3: Perform detection using TensorFlow.js
      console.log('Starting AI detection...');
      const { detections, metrics } = await performDetection(resizedCanvas);
      setPerformance(metrics);
      
      // Step 4: Create processed image with detection overlays
      const processedCanvas = createDetectionCanvas(resizedCanvas, detections, {
        confidenceThreshold: 0.25,
        showConfidence: true,
        showLabels: true
      });
      const processedBlob = await canvasToBlob(processedCanvas);
      
      // Step 5: Save to IndexedDB
      console.log('Saving capture to IndexedDB...');
      const capture = await createCapture(
        frameId,
        originalBlob,    // Original cropped image
        processedBlob,   // Processed image with detections
        detections       // Detection results array
      );
      
      console.log('Capture saved successfully:', {
        captureId: capture.captureId,
        detectionCount: detections.length,
        originalSize: originalBlob.size,
        processedSize: processedBlob.size,
        performance: metrics
      });
      
      // Trigger coordinated UI update to show new capture in history panel
      // Use orchestrated refresh system to prevent conflicts
      setTimeout(() => {
        // Broadcast high-priority refresh trigger for immediate UI update
        broadcastRefreshTrigger('weld-capture-updates', {
          type: 'capture-created',
          captureId: capture.captureId,
          frameId,
          timestamp: Date.now()
        });
        
        // Maintain backward compatibility with context system
        if (captureUpdates) {
          captureUpdates.triggerRefresh();
          captureUpdates.broadcastChange('capture-created', { captureId: capture.captureId, frameId });
        } else if (onCaptureCreated) {
          onCaptureCreated();
        }
      }, 50);
      
    } catch (error) {
      console.error('Error during capture pipeline:', error);
      
      // Show user-friendly error message
      if (error instanceof Error && error.message.includes('model')) {
        alert('AI detection failed. The model may not be loaded correctly. Please refresh the page and try again.');
      } else {
        alert('Failed to capture image. Please try again.');
      }
    } finally {
      setIsCapturing(false);
    }
  };
  
  // Load AI model
  useEffect(() => {
    let mounted = true;
    
    async function loadDetectionModel() {
      try {
        setModelLoading(true);
        setModelError(null);
        
        // Initialize TensorFlow.js
        await tf.ready();
        console.log('TensorFlow.js ready');
        
        // Get optimal model configuration
        const config = modelService.getOptimalModelConfig();
        console.log('Loading model:', config);
        
        // Load model with progress tracking
        const loadedModel = await modelService.loadModel(
          config.modelName,
          (progress: LoadingProgress) => {
            if (mounted) {
              setModelProgress(progress.progress);
            }
          }
        );
        
        if (mounted) {
          // Warm up model
          await modelService.warmupModel(loadedModel, config.inputSize);
          setModel(loadedModel);
          console.log('Model loaded and warmed up successfully');
        }
      } catch (error) {
        console.error('Error loading model:', error);
        if (mounted) {
          setModelError(error instanceof Error ? error.message : 'Failed to load AI model');
        }
      } finally {
        if (mounted) {
          setModelLoading(false);
        }
      }
    }
    
    loadDetectionModel();
    
    return () => {
      mounted = false;
    };
  }, []);

  // Start camera when component mounts
  useEffect(() => {
    startCamera();
    
    // Cleanup on unmount
    return () => {
      stopCamera();
    };
  }, []);
  
  return (
    <>
      <div className="flex-1 relative bg-black">
        {/* Camera Feed */}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-full object-cover"
        />
        
        {/* Canvas for capture (hidden) */}
        <canvas ref={canvasRef} className="hidden" />
        
        {/* Camera Inactive Overlay */}
        {!cameraActive && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80 text-white">
            <div className="text-center">
              <div className="mb-4">Camera inactive</div>
              <button
                onClick={startCamera}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Start Camera
              </button>
            </div>
          </div>
        )}
        
        {/* Model Loading Overlay */}
        {modelLoading && (
          <div className="absolute inset-0 bg-black/70 flex items-center justify-center text-white z-10">
            <div className="text-center">
              <div className="mb-4 w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <div className="text-lg font-medium mb-2">Loading AI Model...</div>
              <div className="text-sm text-gray-300">{Math.round(modelProgress * 100)}%</div>
            </div>
          </div>
        )}

        {/* Model Error Overlay */}
        {modelError && !modelLoading && (
          <div className="absolute inset-0 bg-red-900/70 flex items-center justify-center text-white z-10">
            <div className="text-center max-w-sm p-4">
              <div className="text-lg font-medium mb-2">AI Model Error</div>
              <div className="text-sm text-gray-200 mb-4">{modelError}</div>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Reload Page
              </button>
            </div>
          </div>
        )}

        {/* Resolution Indicator */}
        {cameraActive && !modelLoading && (
          <ResolutionIndicator 
            original={resolution} 
            target={{ width: 640, height: 640 }} 
          />
        )}

        {/* Performance Metrics */}
        {showPerformanceMetrics && performance && (
          <div className="absolute top-2 right-2 bg-black/70 text-white p-2 rounded text-xs">
            <div>Total: {performance.totalTime.toFixed(1)} ms</div>
            <div>Inference: {performance.inferenceTime.toFixed(1)} ms</div>
            <div>Preprocess: {performance.preprocessTime.toFixed(1)} ms</div>
            <div>Postprocess: {performance.postprocessTime.toFixed(1)} ms</div>
          </div>
        )}

        {/* Performance Toggle Button */}
        {performance && !modelLoading && (
          <button
            onClick={() => setShowPerformanceMetrics(!showPerformanceMetrics)}
            className="absolute bottom-2 right-2 bg-black/50 text-white p-1 rounded text-xs hover:bg-black/70"
          >
            {showPerformanceMetrics ? 'Hide' : 'Show'} Metrics
          </button>
        )}
      </div>
      
      {/* Camera Controls */}
      <CameraControls 
        onCapture={captureImage} 
        onToggleCamera={() => {
          if (cameraActive) stopCamera();
          else startCamera();
        }}
        cameraActive={cameraActive && !!model && !modelLoading}
        isCapturing={isCapturing || modelLoading}
      />
    </>
  );
}