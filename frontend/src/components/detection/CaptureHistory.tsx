'use client';

import { useState, useEffect } from 'react';
import { Capture } from '@/lib/db/types';
import { RefreshCw } from 'lucide-react';

interface CaptureHistoryProps {
  frameId: string;
  onSelectCapture: (id: string) => void;
  captures: Capture[];
  isLoading?: boolean;
  onRefresh?: () => void;
}
  
export default function CaptureHistory({ 
  onSelectCapture, 
  captures, 
  isLoading = false, 
  onRefresh 
}: CaptureHistoryProps) {
  const [isClient, setIsClient] = useState(false);
  const [thumbnailUrls, setThumbnailUrls] = useState<Map<string, string>>(new Map());
  
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Create blob URLs for thumbnails
  useEffect(() => {
    const newUrls = new Map<string, string>();
    
    captures.forEach(capture => {
      if (capture.thumbnailBlob) {
        const url = URL.createObjectURL(capture.thumbnailBlob);
        newUrls.set(capture.captureId, url);
      }
    });

    // Cleanup previous URLs
    thumbnailUrls.forEach(url => URL.revokeObjectURL(url));
    
    setThumbnailUrls(newUrls);

    // Return cleanup for new URLs
    return () => {
      newUrls.forEach(url => URL.revokeObjectURL(url));
    };
  }, [captures]);

  // Cleanup URLs on unmount
  useEffect(() => {
    return () => {
      thumbnailUrls.forEach(url => URL.revokeObjectURL(url));
    };
  }, []);
    
  return (
    <div className="h-full flex flex-col">
      {/* Refresh button - fixed at top */}
      {onRefresh && (
        <div className="flex-shrink-0 p-2 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2 py-1 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50"
          >
            <RefreshCw size={12} className={isLoading ? 'animate-spin' : ''} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      )}
      
      {/* Scrollable capture list */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        <div className="divide-y divide-gray-200 dark:divide-gray-700">

      {isLoading && captures.length === 0 ? (
        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Loading captures...</p>
        </div>
      ) : captures.length === 0 ? (
        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
          <p>No captures yet.</p>
          <p className="text-sm mt-2">Use the capture button to take a photo.</p>
        </div>
      ) : (
        captures.map(capture => {
          const thumbnailUrl = thumbnailUrls.get(capture.captureId);
          const objectCount = capture.detectionResults.length;
          
          return (
            <div 
              key={capture.captureId}
              className="p-3 hover:bg-gray-50 dark:hover:bg-gray-750 cursor-pointer transition-colors"
              onClick={() => onSelectCapture(capture.captureId)}
            >
              <div className="flex gap-3">
                {/* Thumbnail */}
                <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden flex-shrink-0">
                  {thumbnailUrl ? (
                    <img 
                      src={thumbnailUrl} 
                      alt={`Capture ${capture.captureId.substring(0, 8)}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // Hide broken images
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-xs text-gray-500">
                      No Image
                    </div>
                  )}
                </div>
                
                {/* Details */}
                <div className="flex-1">
                  <div className="flex justify-between">
                    <div className="font-medium">Capture {capture.captureId.substring(0, 8)}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {isClient ? new Date(capture.captureTimestamp).toLocaleTimeString() : ''}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {objectCount} object{objectCount !== 1 ? 's' : ''} detected
                  </div>
                  
                  {/* Detection Classes */}
                  {objectCount > 0 && (
                    <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                      {Array.from(new Set(capture.detectionResults.map(d => d.class))).slice(0, 3).join(', ')}
                      {capture.detectionResults.length > 3 && '...'}
                    </div>
                  )}
                  
                  {/* Sync Status */}
                  <div className="mt-2 flex items-center justify-between">
                    <div className="flex gap-1">
                      <span className="inline-block px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs">
                        View
                      </span>
                      <span className="inline-block px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-xs">
                        Export
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        capture.syncStatus === 'synced' ? 'bg-green-500' : 
                        capture.syncStatus === 'pending' ? 'bg-yellow-500' : 
                        'bg-red-500'
                      }`}></div>
                      <span className="text-xs text-gray-400 capitalize">{capture.syncStatus}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })
        )}
        </div>
      </div>
    </div>
  );
} 