// src/components/detection/HistoryPanel.tsx
'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import FrameManager from '@/components/detection/FrameManager';
import CaptureHistory from '@/components/detection/CaptureHistory';
import CaptureDetails from '@/components/detection/CaptureDetails';
import { getCapturesByFrameId } from '@/lib/db/captureOperations';
import { Capture } from '@/lib/db/types';
import { FrameSyncStatusDetailed } from '@/components/sync/FrameSyncStatus';
import { useOrchestratedRefresh, REFRESH_PRESETS, broadcastRefreshTrigger } from '@/hooks/useOrchestratedRefresh';
import { useCaptureUpdates } from '@/context/CaptureUpdatesContext';

interface HistoryPanelProps {
  frameId: string;
}

export default function HistoryPanel({ frameId }: HistoryPanelProps) {
  const [selectedCaptureId, setSelectedCaptureId] = useState<string | null>(null);
  const [captures, setCaptures] = useState<Capture[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get context for global capture updates
  const { broadcastChange } = useCaptureUpdates();

  // Load captures from IndexedDB with smart state updates
  const loadCaptures = useCallback(async () => {
    if (!frameId) return;
    
    try {
      // Use different loading states based on whether we have data
      const hasExistingData = captures.length > 0;
      if (!hasExistingData) {
        setIsLoading(true);
      }
      setError(null);
      
      const frameCaptures = await getCapturesByFrameId(frameId);
      
      // Always update captures to ensure statistics are fresh
      setCaptures(frameCaptures);
    } catch (err) {
      console.error('Error loading captures:', err);
      setError(err instanceof Error ? err.message : 'Failed to load captures');
    } finally {
      setIsLoading(false);
    }
  }, [frameId, captures.length]);

  // Orchestrated refresh system - replaces triple hook pattern
  const {
    refresh: refreshCaptures
  } = useOrchestratedRefresh(loadCaptures, {
    key: `load-captures-${frameId}`,
    ...REFRESH_PRESETS.AUTO_REFRESH,
    broadcastChannel: 'weld-capture-updates',
    context: { frameId },
    onRefreshStart: () => {
      // Show refreshing state for existing data
      if (captures.length > 0) {
        // Could set a subtle refreshing indicator here if needed
      }
    }
  });

  // Load captures when frameId changes
  useEffect(() => {
    loadCaptures();
  }, [loadCaptures]);

  // Memoized expensive calculations - only recalculate when captures change
  const statistics = useMemo(() => {
    const totalObjects = captures.reduce((sum, capture) => sum + capture.detectionResults.length, 0);
    
    const avgConfidence = captures.length > 0 
      ? captures.reduce((sum, capture) => {
          const captureAvg = capture.detectionResults.length > 0
            ? capture.detectionResults.reduce((captureSum, detection) => captureSum + detection.confidence, 0) / capture.detectionResults.length
            : 0;
          return sum + captureAvg;
        }, 0) / captures.length
      : 0;

    const syncStats = {
      synced: captures.filter(c => c.syncStatus === 'synced').length,
      pending: captures.filter(c => c.syncStatus === 'pending').length,
      failed: captures.filter(c => c.syncStatus === 'conflict').length
    };
    
    return { totalObjects, avgConfidence, syncStats };
  }, [captures]);
  
  const { totalObjects, avgConfidence, syncStats } = statistics;

  // Manual refresh function with cross-tab broadcasting
  const handleManualRefresh = useCallback(() => {
    refreshCaptures('user-action');
    // Broadcast change to other tabs and components
    broadcastRefreshTrigger('weld-capture-updates', { 
      type: 'manual-refresh', 
      frameId 
    });
    broadcastChange('capture-manual-refresh', { frameId });
  }, [refreshCaptures, broadcastChange, frameId]);
  
  // Stable callback for capture selection
  const handleCaptureSelect = useCallback((captureId: string) => {
    setSelectedCaptureId(captureId);
  }, []);
  
  // Stable callback for back navigation
  const handleBack = useCallback(() => {
    setSelectedCaptureId(null);
  }, []);

  return (
    <div className="h-full flex flex-col">
      {/* Frame Manager Header */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
        <FrameManager frameId={frameId} captureCount={captures.length} />
      </div>
      
      {/* Panel Content */}
      <div className="flex-1 min-h-0 overflow-hidden flex flex-col">
        {selectedCaptureId ? (
          // Selected capture details view
          <div className="flex-1 overflow-auto">
            <CaptureDetails 
              captureId={selectedCaptureId} 
              onBack={handleBack}
            />
          </div>
        ) : (
          // Capture history list
          <div className="flex-1 min-h-0 overflow-hidden">
            {error ? (
              <div className="p-8 text-center text-red-500 dark:text-red-400">
                <p>Error loading captures</p>
                <p className="text-sm mt-2">{error}</p>
                <button 
                  onClick={handleManualRefresh}
                  className="mt-4 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  Retry
                </button>
              </div>
            ) : (
              <CaptureHistory 
                frameId={frameId} 
                onSelectCapture={handleCaptureSelect}
                captures={captures}
                isLoading={isLoading}
                onRefresh={handleManualRefresh}
              />
            )}
          </div>
        )}
      </div>
      
      {/* Statistics Footer */}
      <div className="flex-shrink-0 p-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 text-xs text-gray-500 dark:text-gray-400">
        {isLoading && captures.length === 0 ? (
          <div className="flex justify-center">
            <div className="text-center">Loading statistics...</div>
          </div>
        ) : (
          <div className="space-y-2">
            {/* Main Statistics */}
            <div className="flex justify-between">
              <div>Total Captures: {captures.length}</div>
              <div>Objects Detected: {totalObjects}</div>
              <div>Avg. Confidence: {avgConfidence > 0 ? Math.round(avgConfidence * 100) : 0}%</div>
            </div>
            
            {/* Sync Statistics */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
              {/* <div className="flex space-x-4">
                {syncStats.synced > 0 && <span className="text-green-600">✅ {syncStats.synced} synced</span>}
                {syncStats.pending > 0 && <span className="text-yellow-600">⏳ {syncStats.pending} pending</span>}
                {syncStats.failed > 0 && <span className="text-red-600">❌ {syncStats.failed} failed</span>}
                {captures.length === 0 && <span>No captures yet</span>}
              </div>
               */}
              {/* Frame-Specific Sync Controls */}
              <div className="ml-auto">
                <FrameSyncStatusDetailed 
                  frameId={frameId}
                  captures={captures}
                  className="scale-90"
                  onSyncComplete={handleManualRefresh}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}