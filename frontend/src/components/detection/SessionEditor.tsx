// src/components/detection/SessionEditor.tsx
'use client';

import { useState, useEffect } from 'react';
import { Edit, Save, X, Loader2, Cloud, CloudOff } from 'lucide-react';
import { useSession } from '@/context/SessionContext';
import { updateFrame } from '@/lib/db/frameOperations';
import { useOptionalCaptureUpdates } from '@/hooks/useOptionalCaptureUpdates';

interface SessionEditorProps {
  onClose?: () => void;
  compact?: boolean;
}

export default function SessionEditor({ onClose, compact = false }: SessionEditorProps) {
  const { currentFrame, loadFrame } = useSession();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [formData, setFormData] = useState({
    modelNumber: '',
    machineSerialNumber: '',
    inspectorName: ''
  });
  
  // Get context for broadcasting updates (optional)
  const captureUpdates = useOptionalCaptureUpdates();

  // Initialize form data when frame loads
  useEffect(() => {
    if (currentFrame) {
      setFormData({
        modelNumber: currentFrame.modelNumber,
        machineSerialNumber: currentFrame.machineSerialNumber,
        inspectorName: currentFrame.inspectorName
      });
    }
  }, [currentFrame]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    if (currentFrame) {
      setFormData({
        modelNumber: currentFrame.modelNumber,
        machineSerialNumber: currentFrame.machineSerialNumber,
        inspectorName: currentFrame.inspectorName
      });
    }
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (!currentFrame) return;

    setIsSaving(true);
    setSaveSuccess(false);
    
    try {
      // Update frame in IndexedDB and queue for sync
      await updateFrame(currentFrame.frameId, {
        modelNumber: formData.modelNumber.trim(),
        machineSerialNumber: formData.machineSerialNumber.trim(),
        inspectorName: formData.inspectorName.trim()
      });

      // Reload the frame to get updated data
      await loadFrame(currentFrame.frameId);
      
      // Broadcast change to trigger sync and update other components
      if (captureUpdates) {
        captureUpdates.broadcastChange('frame-updated', { 
          frameId: currentFrame.frameId,
          changes: ['modelNumber', 'machineSerialNumber', 'inspectorName']
        });
      }
      
      // Show success state briefly
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 2000);
      
      setIsEditing(false);
      
      // Close dialog after successful save if onClose is provided
      if (onClose && !compact) {
        setTimeout(() => onClose(), 1000);
      }
    } catch (error) {
      console.error('Error updating frame:', error);
      alert('Failed to save changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const isFormValid = () => {
    return formData.modelNumber.trim() && 
           formData.machineSerialNumber.trim() && 
           formData.inspectorName.trim();
  };

  if (!currentFrame) {
    return null;
  }

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {isEditing ? (
          <div className="flex items-center gap-2">
            <button
              onClick={handleSave}
              disabled={!isFormValid() || isSaving}
              className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Save changes"
            >
              {isSaving ? (
                <Loader2 size={16} className="animate-spin" />
              ) : saveSuccess ? (
                <Cloud size={16} className="text-green-600" />
              ) : (
                <Save size={16} />
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50"
              title="Cancel editing"
            >
              <X size={16} />
            </button>
          </div>
        ) : (
          <button
            onClick={handleEdit}
            className="p-1 text-blue-600 hover:text-blue-700"
            title="Edit session"
          >
            <Edit size={16} />
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Session Information</h3>
          <div className="flex items-center gap-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  disabled={!isFormValid() || isSaving}
                  className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                >
                  {isSaving ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : saveSuccess ? (
                    <Cloud size={14} className="text-green-600" />
                  ) : (
                    <Save size={14} />
                  )}
                  {isSaving ? 'Saving...' : saveSuccess ? 'Saved!' : 'Save'}
                </button>
                <button
                  onClick={handleCancel}
                  disabled={isSaving}
                  className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:opacity-50 flex items-center gap-1"
                >
                  <X size={14} />
                  Cancel
                </button>
              </>
            ) : (
              <button
                onClick={handleEdit}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center gap-1"
              >
                <Edit size={14} />
                Edit
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Model Number *
          </label>
          {isEditing ? (
            <input
              type="text"
              value={formData.modelNumber}
              onChange={(e) => handleInputChange('modelNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter model number"
              disabled={isSaving}
            />
          ) : (
            <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md text-sm">
              {currentFrame.modelNumber}
            </div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Machine Serial Number *
          </label>
          {isEditing ? (
            <input
              type="text"
              value={formData.machineSerialNumber}
              onChange={(e) => handleInputChange('machineSerialNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter machine serial number"
              disabled={isSaving}
            />
          ) : (
            <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md text-sm">
              {currentFrame.machineSerialNumber}
            </div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Inspector Name *
          </label>
          {isEditing ? (
            <input
              type="text"
              value={formData.inspectorName}
              onChange={(e) => handleInputChange('inspectorName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter inspector name"
              disabled={isSaving}
            />
          ) : (
            <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md text-sm">
              {currentFrame.inspectorName}
            </div>
          )}
        </div>

        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <div>Frame ID: {currentFrame.frameId}</div>
            <div>Created: {new Date(currentFrame.creationTimestamp).toLocaleString()}</div>
            <div>Last Modified: {new Date(currentFrame.lastModifiedTimestamp).toLocaleString()}</div>
            <div>Status: <span className="capitalize">{currentFrame.status}</span></div>
            <div className="flex items-center gap-2">
              <span>Sync Status:</span>
              <div className="flex items-center gap-1">
                {currentFrame.syncStatus === 'synced' ? (
                  <>
                    <Cloud size={12} className="text-green-600" />
                    <span className="text-green-600">Synced</span>
                  </>
                ) : currentFrame.syncStatus === 'pending' ? (
                  <>
                    <Loader2 size={12} className="text-yellow-600 animate-spin" />
                    <span className="text-yellow-600">Pending</span>
                  </>
                ) : (
                  <>
                    <CloudOff size={12} className="text-red-600" />
                    <span className="text-red-600">Failed</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}