'use client';

import { useState, useEffect } from 'react';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock, RefreshCw, AlertCircle } from 'lucide-react';
import { startSync, getSyncStatus, stopSync, isSyncing } from '@/lib/sync/syncManager';
import { syncStateManager } from '@/lib/sync/syncStateManager';
import { useSyncProgress } from '@/hooks/useSyncEvents';

interface SyncStatusProps {
  showDetails?: boolean;
  className?: string;
}

interface SyncStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

interface SyncProgress {
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  currentItem?: string;
  isProcessing: boolean;
}

export function SyncStatus({ showDetails = false, className = '' }: SyncStatusProps) {
  const [stats, setStats] = useState<SyncStats>({ pending: 0, processing: 0, completed: 0, failed: 0 });
  const [progress, setProgress] = useState<SyncProgress | null>(null);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Removed updateStats function - now handled inline to prevent infinite loops

  // Subscribe to sync state manager - single source of truth
  useEffect(() => {
    // Initial load - call updateStats once on mount
    const loadInitialStats = async () => {
      try {
        const currentStats = await getSyncStatus();
        setStats(currentStats);
        setSyncing(isSyncing());
      } catch (err) {
        console.error('Failed to get initial sync stats:', err);
      }
    };

    loadInitialStats();

    // Subscribe to reactive state updates from sync state manager
    const subscription = syncStateManager.getSyncState().subscribe((syncState) => {
      setStats(syncState.stats);
      setProgress(syncState.progress);
      setSyncing(syncState.isProcessing);
    });

    return () => subscription.unsubscribe();
  }, []); // No dependencies to prevent infinite loop

  // Enhanced: Direct progress monitoring for real-time updates
  // This provides more granular progress updates than the state manager
  useSyncProgress((progressUpdate) => {
    if (progressUpdate) {
      setProgress(progressUpdate);
      setSyncing(progressUpdate.isProcessing);
    } else {
      // Progress cleared - sync completed or stopped
      setProgress(null);
      setSyncing(false);
    }
    // Don't call updateStats here - let the state manager handle it
  });

  const handleManualSync = async () => {
    if (syncing) {
      // Stop current sync
      stopSync();
      setSyncing(false);
      setProgress(null);
      return;
    }

    try {
      setError(null);
      setSyncing(true);

      // Enhanced: Progress updates are now handled by useSyncProgress hook
      // This eliminates duplicate progress handling
      await startSync();

      // Stats will be updated automatically by useSyncProgress when sync completes

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sync failed');
      // Reset state on error
      setSyncing(false);
      setProgress(null);
    }
    // Note: Don't set finally block here - let useSyncProgress handle state cleanup
  };

  const getSyncStatusIcon = () => {
    if (syncing) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
    }
    
    if (stats.failed > 0) {
      return <AlertCircle className="w-4 h-4 text-red-600" />;
    }
    
    if (stats.pending > 0) {
      return <Clock className="w-4 h-4 text-yellow-600" />;
    }
    
    return <CheckCircle className="w-4 h-4 text-green-600" />;
  };

  const getSyncStatusText = () => {
    if (syncing) {
      return 'Syncing...';
    }
    
    if (stats.failed > 0) {
      return `${stats.failed} failed`;
    }
    
    if (stats.pending > 0) {
      return `${stats.pending} pending`;
    }
    
    return 'Up to date';
  };

  const getProgressPercentage = () => {
    if (!progress || progress.totalItems === 0) return 0;
    return Math.round((progress.processedItems / progress.totalItems) * 100);
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Sync Status Icon and Text */}
      <div className="flex items-center space-x-1">
        {getSyncStatusIcon()}
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {getSyncStatusText()}
        </span>
      </div>

      {/* Manual Sync Button */}
      <button
        onClick={handleManualSync}
        disabled={!navigator.onLine}
        className={`p-1 rounded-md transition-colors ${
          syncing
            ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'
            : 'text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20'
        } ${!navigator.onLine ? 'opacity-50 cursor-not-allowed' : ''}`}
        title={syncing ? 'Stop sync' : 'Start manual sync'}
      >
        {syncing ? (
          <XCircle className="w-4 h-4" />
        ) : (
          <RefreshCw className="w-4 h-4" />
        )}
      </button>

      {/* Progress Details (when syncing) */}
      {progress && showDetails && (
        <div className="flex flex-col space-y-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
              <div 
                className="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                style={{ width: `${getProgressPercentage()}%` }}
              ></div>
            </div>
            <span className="text-xs text-gray-500 whitespace-nowrap">
              {progress.processedItems}/{progress.totalItems}
            </span>
          </div>
          {progress.currentItem && (
            <span className="text-xs text-gray-400 truncate">
              {progress.currentItem}
            </span>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-xs text-red-600 dark:text-red-400 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}

      {/* Detailed Stats (when expanded) */}
      {showDetails && !progress && (
        <div className="text-xs text-gray-500 space-x-2">
          {stats.pending > 0 && <span>⏳ {stats.pending}</span>}
          {stats.failed > 0 && <span>❌ {stats.failed}</span>}
          {stats.completed > 0 && <span>✅ {stats.completed}</span>}
        </div>
      )}
    </div>
  );
}

// Compact version for headers/toolbars
export function SyncStatusCompact({ className = '' }: { className?: string }) {
  return <SyncStatus showDetails={false} className={className} />;
}

// Detailed version for panels/modals
export function SyncStatusDetailed({ className = '' }: { className?: string }) {
  return <SyncStatus showDetails={true} className={className} />;
}