'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Loader2, CheckCircle, XCircle, Clock, RefreshCw, AlertCircle } from 'lucide-react';
import { startFrameSync, getFrameSyncStatus, stopSync } from '@/lib/sync/syncManager';
import { useSyncState, useSyncProgress } from '@/hooks/useSyncEvents';
import { Capture } from '@/lib/db/types';

interface FrameSyncStatusProps {
  frameId: string;
  captures: Capture[];
  className?: string;
  onSyncComplete?: () => void;
  showDetails?: boolean;
}

interface FrameSyncStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

interface SyncProgress {
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  currentItem?: string;
  isProcessing: boolean;
}

export function FrameSyncStatus({
  frameId,
  captures,
  className = '',
  onSyncComplete,
  showDetails = true
}: FrameSyncStatusProps) {
  const [syncStats, setSyncStats] = useState<FrameSyncStats>({ pending: 0, processing: 0, completed: 0, failed: 0 });
  const [progress, setProgress] = useState<SyncProgress | null>(null);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate frame-specific sync stats from captures (fallback)
  const captureStats = useMemo(() => ({
    synced: captures.filter(c => c.syncStatus === 'synced').length,
    pending: captures.filter(c => c.syncStatus === 'pending').length,
    failed: captures.filter(c => c.syncStatus === 'conflict').length,
    total: captures.length
  }), [captures]);

  // Use sync stats from database when available, fallback to capture stats
  const effectiveStats = useMemo(() => {
    // If we have fresh sync stats from the database, use those
    if (syncStats.pending > 0 || syncStats.failed > 0 || syncStats.completed > 0) {
      return {
        synced: syncStats.completed,
        pending: syncStats.pending,
        failed: syncStats.failed,
        total: syncStats.pending + syncStats.completed + syncStats.failed
      };
    }
    // Otherwise fallback to capture stats
    return captureStats;
  }, [syncStats, captureStats]);

  // Enhanced sync state monitoring for frame-specific updates
  const lastProcessingState = useRef(false);

  // Monitor global sync state for frame-specific changes
  useSyncState((syncState) => {
    console.log('[FrameSyncStatus] Sync state update:', {
      frameId,
      isProcessing: syncState.isProcessing,
      lastProcessingState: lastProcessingState.current,
      stats: syncState.stats,
      progress: syncState.progress
    });

    // Update syncing state from global sync manager
    setSyncing(syncState.isProcessing);

    // Clear progress when sync completes to prevent stale progress bar
    if (!syncState.isProcessing) {
      setProgress(null);
    }

    // When sync starts, refresh frame stats to show current state
    if (!lastProcessingState.current && syncState.isProcessing) {
      console.log('[FrameSyncStatus] Sync started, refreshing frame stats for:', frameId);
      // Refresh frame stats when sync starts to show updated pending counts
      const refreshFrameStats = async () => {
        try {
          const frameStats = await getFrameSyncStatus(frameId);
          setSyncStats(frameStats);
        } catch (err) {
          console.error('Failed to refresh frame stats on sync start:', err);
        }
      };
      refreshFrameStats();
    }

    // When sync completes, trigger parent to reload captures and refresh stats
    if (lastProcessingState.current && !syncState.isProcessing) {
      console.log('[FrameSyncStatus] Sync completed, triggering onSyncComplete for frame:', frameId);
      if (onSyncComplete) {
        onSyncComplete();
      }

      // Also refresh frame stats after sync completion
      const refreshFrameStats = async () => {
        try {
          const frameStats = await getFrameSyncStatus(frameId);
          setSyncStats(frameStats);
        } catch (err) {
          console.error('Failed to refresh frame stats on sync completion:', err);
        }
      };
      refreshFrameStats();
    }
    lastProcessingState.current = syncState.isProcessing;
  });

  // Monitor sync progress for real-time updates
  useSyncProgress((progressUpdate) => {
    if (progressUpdate && progressUpdate.isProcessing) {
      // Only set progress when actively processing
      setProgress(progressUpdate);

      // Refresh frame stats periodically during sync to show real-time updates
      const refreshFrameStats = async () => {
        try {
          const frameStats = await getFrameSyncStatus(frameId);
          setSyncStats(frameStats);
        } catch (err) {
          console.error('Failed to refresh frame stats during sync:', err);
        }
      };
      refreshFrameStats();
    } else {
      // Clear progress when not processing or progress is null
      setProgress(null);
    }
    // Don't update syncing state here - let useSyncState handle it to avoid conflicts
  });

  // Update sync stats when frameId changes
  useEffect(() => {
    const updateFrameStats = async () => {
      try {
        const frameStats = await getFrameSyncStatus(frameId);
        setSyncStats(frameStats);
      } catch (err) {
        console.error('Failed to get frame sync stats:', err);
      }
    };

    updateFrameStats();
  }, [frameId]); // Only update when frameId changes

  const handleFrameSync = useCallback(async () => {
    if (syncing) {
      // Stop current sync
      stopSync();
      setSyncing(false);
      setProgress(null);
      return;
    }

    try {
      setError(null);
      setSyncing(true);

      // Enhanced: Progress updates handled by useSyncProgress hook
      await startFrameSync(frameId);

      // Parent component will be notified via useSyncState hook when sync completes

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Frame sync failed');
      // Reset state on error
      setSyncing(false);
      setProgress(null);
    }
    // Note: Don't use finally block - let useSyncProgress handle state cleanup
  }, [frameId, syncing]);

  const getSyncStatusIcon = () => {
    if (syncing) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
    }
    
    // Use effective stats for more accurate representation
    if (effectiveStats.failed > 0) {
      return <AlertCircle className="w-4 h-4 text-red-600" />;
    }

    if (effectiveStats.pending > 0) {
      return <Clock className="w-4 h-4 text-yellow-600" />;
    }

    if (effectiveStats.total === 0) {
      return <Clock className="w-4 h-4 text-gray-400" />;
    }
    
    return <CheckCircle className="w-4 h-4 text-green-600" />;
  };

  const getSyncStatusText = () => {
    if (syncing) {
      return 'Syncing frame...';
    }
    
    if (effectiveStats.failed > 0) {
      return `${effectiveStats.failed} failed`;
    }

    if (effectiveStats.pending > 0) {
      return `${effectiveStats.pending} pending`;
    }

    if (effectiveStats.total === 0) {
      return 'No captures';
    }
    
    return 'Frame synced';
  };

  const getProgressPercentage = () => {
    if (!progress || progress.totalItems === 0) return 0;
    return Math.round((progress.processedItems / progress.totalItems) * 100);
  };

  const needsSync = effectiveStats.pending > 0 || effectiveStats.failed > 0;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Sync Status Icon and Text */}
      <div className="flex items-center space-x-1">
        {getSyncStatusIcon()}
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {getSyncStatusText()}
        </span>
      </div>

      {/* Manual Sync Button - only show if there are items to sync */}
      {needsSync && (
        <button
          onClick={handleFrameSync}
          disabled={!navigator.onLine}
          className={`p-1 rounded-md transition-colors ${
            syncing
              ? 'text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'
              : 'text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20'
          } ${!navigator.onLine ? 'opacity-50 cursor-not-allowed' : ''}`}
          title={syncing ? 'Stop frame sync' : `Sync ${effectiveStats.pending + effectiveStats.failed} items from this frame`}
        >
          {syncing ? (
            <XCircle className="w-4 h-4" />
          ) : (
            <RefreshCw className="w-4 h-4" />
          )}
        </button>
      )}

      {/* Progress Details (when syncing) */}
      {progress && syncing && showDetails && (
        <div className="flex flex-col space-y-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
              <div 
                className="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                style={{ width: `${getProgressPercentage()}%` }}
              ></div>
            </div>
            <span className="text-xs text-gray-500 whitespace-nowrap">
              {progress.processedItems}/{progress.totalItems}
            </span>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-xs text-red-600 dark:text-red-400 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}

      {/* Detailed Stats (when expanded) */}
      {showDetails && !progress && effectiveStats.total > 0 && (
        <div className="text-xs text-gray-500 space-x-2">
          {effectiveStats.pending > 0 && <span>⏳ {effectiveStats.pending}</span>}
          {effectiveStats.failed > 0 && <span>❌ {effectiveStats.failed}</span>}
          {effectiveStats.synced > 0 && <span>✅ {effectiveStats.synced}</span>}
        </div>
      )}
    </div>
  );
}

// Compact version for headers/toolbars
export function FrameSyncStatusCompact({ 
  frameId, 
  captures, 
  className = '',
  onSyncComplete 
}: Omit<FrameSyncStatusProps, 'showDetails'>) {
  return (
    <FrameSyncStatus 
      frameId={frameId}
      captures={captures}
      showDetails={false} 
      className={className}
      onSyncComplete={onSyncComplete}
    />
  );
}

// Detailed version for panels/modals
export function FrameSyncStatusDetailed({ 
  frameId, 
  captures, 
  className = '',
  onSyncComplete 
}: Omit<FrameSyncStatusProps, 'showDetails'>) {
  return (
    <FrameSyncStatus 
      frameId={frameId}
      captures={captures}
      showDetails={true} 
      className={className}
      onSyncComplete={onSyncComplete}
    />
  );
}