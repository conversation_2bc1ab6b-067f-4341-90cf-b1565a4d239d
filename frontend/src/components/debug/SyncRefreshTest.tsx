'use client';

import { useState } from 'react';
import { notifySyncCompleted } from '@/lib/refresh/RefreshOrchestrator';
import { broadcastRefreshTrigger } from '@/hooks/useOrchestratedRefresh';

/**
 * Debug component to test sync event integration with refresh coordination
 * This demonstrates that session cards will auto-refresh when sync completes
 */
export default function SyncRefreshTest() {
  const [events, setEvents] = useState<string[]>([]);
  
  const addEvent = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setEvents(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const testSyncCompleted = () => {
    addEvent('🔄 Simulating sync completion...');
    
    // Trigger sync completed event through orchestrator
    notifySyncCompleted();
    
    addEvent('✅ Sync completed event emitted - SessionList should auto-refresh');
    
    // Also broadcast session update
    broadcastRefreshTrigger('session-updates', {
      type: 'sync-completed',
      timestamp: Date.now()
    });
    
    addEvent('📡 Session update broadcast sent');
  };

  const testSessionUpdate = () => {
    addEvent('📝 Simulating session status change...');
    
    // Broadcast session update
    broadcastRefreshTrigger('session-updates', {
      type: 'session-updated',
      timestamp: Date.now()
    });
    
    addEvent('🔄 Session update triggered - Cards should refresh');
  };

  const testCaptureCreated = () => {
    addEvent('📸 Simulating capture creation...');
    
    // Broadcast capture update
    broadcastRefreshTrigger('weld-capture-updates', {
      type: 'capture-created',
      captureId: 'test-capture-' + Date.now(),
      frameId: 'test-frame',
      timestamp: Date.now()
    });
    
    addEvent('🔄 Capture update triggered - History should refresh');
  };

  const clearEvents = () => {
    setEvents([]);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        🧪 Sync Refresh Test
      </h3>
      
      <div className="space-y-3 mb-4">
        <button
          onClick={testSyncCompleted}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Test Sync Completion
        </button>
        
        <button
          onClick={testSessionUpdate}
          className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          Test Session Update
        </button>
        
        <button
          onClick={testCaptureCreated}
          className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Test Capture Created
        </button>
        
        <button
          onClick={clearEvents}
          className="w-full px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
        >
          Clear Events
        </button>
      </div>
      
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Event Log:
        </h4>
        <div className="space-y-1 max-h-40 overflow-y-auto">
          {events.length === 0 ? (
            <p className="text-xs text-gray-500 dark:text-gray-400 italic">
              No events yet. Click buttons above to test.
            </p>
          ) : (
            events.map((event, index) => (
              <p key={index} className="text-xs text-gray-600 dark:text-gray-400 font-mono">
                {event}
              </p>
            ))
          )}
        </div>
      </div>
      
      <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
        <strong>What should happen:</strong>
        <ul className="mt-1 space-y-1 list-disc list-inside">
          <li>Sync completion → Session cards refresh automatically</li>
          <li>Session updates → All components sync status updates</li>
          <li>Capture creation → History panel shows new captures</li>
        </ul>
      </div>
    </div>
  );
}