'use client';

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRoles?: ('admin' | 'inspector')[];
  fallbackPath?: string;
  loadingComponent?: ReactNode;
}

export function ProtectedRoute({ 
  children, 
  requiredRoles = ['admin', 'inspector'],
  fallbackPath = '/login',
  loadingComponent = <div className="flex items-center justify-center min-h-screen">Loading...</div>
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        // Redirect to login with current path as redirect parameter
        const currentPath = window.location.pathname;
        const loginUrl = `${fallbackPath}?redirect=${encodeURIComponent(currentPath)}`;
        router.push(loginUrl);
        return;
      }

      if (user && !requiredRoles.includes(user.role)) {
        // User doesn't have required role
        router.push('/unauthorized');
        return;
      }
    }
  }, [isAuthenticated, isLoading, user, router, requiredRoles, fallbackPath]);

  // Show loading while checking authentication
  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  // Show loading while redirecting
  if (!isAuthenticated || (user && !requiredRoles.includes(user.role))) {
    return <>{loadingComponent}</>;
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRoles'>) {
  return (
    <ProtectedRoute {...props} requiredRoles={['admin']}>
      {children}
    </ProtectedRoute>
  );
}

export function InspectorRoute({ children, ...props }: Omit<ProtectedRouteProps, 'requiredRoles'>) {
  return (
    <ProtectedRoute {...props} requiredRoles={['inspector', 'admin']}>
      {children}
    </ProtectedRoute>
  );
}