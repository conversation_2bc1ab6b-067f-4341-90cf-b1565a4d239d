'use client';

import { useAuth } from '@/context/AuthContext';
import { ReactNode } from 'react';

interface AuthGuardProps {
  children: ReactNode;
  requiredRoles?: ('admin' | 'inspector')[];
  fallback?: ReactNode;
  showFallbackWhenLoading?: boolean;
}

export function AuthGuard({ 
  children, 
  requiredRoles = ['admin', 'inspector'],
  fallback = null,
  showFallbackWhenLoading = false
}: AuthGuardProps) {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Show fallback while loading if specified
  if (isLoading && showFallbackWhenLoading) {
    return <>{fallback}</>;
  }

  // Don't render anything while loading unless showFallbackWhenLoading is true
  if (isLoading) {
    return null;
  }

  // Not authenticated
  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  // User doesn't have required role
  if (user && !requiredRoles.includes(user.role)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Convenience components
export function AdminOnly({ children, fallback = null }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requiredRoles={['admin']} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

export function AuthenticatedOnly({ children, fallback = null }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requiredRoles={['admin', 'inspector']} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}