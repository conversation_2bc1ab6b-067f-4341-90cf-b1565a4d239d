'use client';

import { createContext, useContext, useCallback, useMemo, ReactNode } from 'react';
import { useIndexedDBWatcher } from '@/hooks/useIndexedDBWatcher';
import { refreshOrchestrator } from '@/lib/refresh/RefreshOrchestrator';

interface CaptureUpdatesContextType {
  triggerRefresh: () => void;
  broadcastChange: (changeType: string, data?: unknown) => void;
}

const CaptureUpdatesContext = createContext<CaptureUpdatesContextType | undefined>(undefined);

interface CaptureUpdatesProviderProps {
  children: ReactNode;
  onCaptureUpdate?: () => void;
}

export function CaptureUpdatesProvider({ 
  children, 
  onCaptureUpdate 
}: CaptureUpdatesProviderProps) {
  
  // Handle data changes from cross-tab communication
  const handleDataChange = useCallback(() => {
    if (onCaptureUpdate) {
      onCaptureUpdate();
    }
  }, [onCaptureUpdate]);
  
  // Set up cross-tab communication
  const { broadcastChange } = useIndexedDBWatcher(handleDataChange, {
    channel: 'weld-capture-updates',
    debounceMs: 200,
    enabled: true
  });
  
  // Trigger refresh function for manual updates with orchestrator integration
  const triggerRefresh = useCallback(() => {
    // Use orchestrator for coordinated refresh
    if (onCaptureUpdate) {
      refreshOrchestrator.requestRefresh(
        'capture-context-refresh',
        async () => {
          onCaptureUpdate();
        },
        {
          priority: 'high',
          trigger: 'user-action',
          debounceMs: 100
        }
      ).catch(error => {
        console.warn('Orchestrated refresh failed:', error);
        // Fallback to direct call
        onCaptureUpdate();
      });
    }
    
    // Broadcast to other tabs
    broadcastChange('capture-refresh');
  }, [broadcastChange, onCaptureUpdate]);
  
  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    triggerRefresh,
    broadcastChange
  }), [triggerRefresh, broadcastChange]);
  
  return (
    <CaptureUpdatesContext.Provider value={contextValue}>
      {children}
    </CaptureUpdatesContext.Provider>
  );
}

export function useCaptureUpdates() {
  const context = useContext(CaptureUpdatesContext);
  if (context === undefined) {
    throw new Error('useCaptureUpdates must be used within a CaptureUpdatesProvider');
  }
  return context;
}

// Higher-order component for easy integration
export function withCaptureUpdates<P extends object>(
  Component: React.ComponentType<P>
) {
  return function WrappedComponent(props: P) {
    return (
      <CaptureUpdatesProvider>
        <Component {...props} />
      </CaptureUpdatesProvider>
    );
  };
}