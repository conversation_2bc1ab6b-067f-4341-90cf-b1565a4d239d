'use client';

import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';

export interface User {
  user_id: string;
  username: string;
  full_name: string;
  email: string;
  role: 'admin' | 'inspector';
  is_active: boolean;
  created_at: number;
  last_login?: number;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  refreshTokens: () => Promise<boolean>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

export interface RegisterData {
  username: string;
  email: string;
  full_name: string;
  password: string;
  role?: 'admin' | 'inspector';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Token management utilities
const TOKEN_STORAGE_KEY = 'auth_tokens';
const USER_STORAGE_KEY = 'auth_user';

const getStoredTokens = (): AuthTokens | null => {
  if (typeof window === 'undefined') return null;
  try {
    const stored = localStorage.getItem(TOKEN_STORAGE_KEY);
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
};

const setStoredTokens = (tokens: AuthTokens | null) => {
  if (typeof window === 'undefined') return;
  if (tokens) {
    localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokens));
    // Also set cookies for middleware - encode JSON properly
    const encodedTokens = encodeURIComponent(JSON.stringify(tokens));
    document.cookie = `auth_tokens=${encodedTokens}; path=/; max-age=${tokens.expires_in}; SameSite=Lax`;
  } else {
    localStorage.removeItem(TOKEN_STORAGE_KEY);
    // Clear cookies
    document.cookie = 'auth_tokens=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
  }
};

const getStoredUser = (): User | null => {
  if (typeof window === 'undefined') return null;
  try {
    const stored = localStorage.getItem(USER_STORAGE_KEY);
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
};

const setStoredUser = (user: User | null) => {
  if (typeof window === 'undefined') return;
  if (user) {
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
    // Also set cookies for middleware - encode JSON properly
    const encodedUser = encodeURIComponent(JSON.stringify(user));
    document.cookie = `auth_user=${encodedUser}; path=/; max-age=604800; SameSite=Lax`; // 7 days
  } else {
    localStorage.removeItem(USER_STORAGE_KEY);
    // Clear cookies
    document.cookie = 'auth_user=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
  }
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // API base URL
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

  // Initialize auth state from storage
  useEffect(() => {
    const storedTokens = getStoredTokens();
    const storedUser = getStoredUser();
    
    if (storedTokens && storedUser) {
      setTokens(storedTokens);
      setUser(storedUser);
      // Try to refresh tokens to ensure they're still valid
      refreshTokens();
    } else {
      setIsLoading(false);
    }
  }, []);

  // Auto-refresh tokens before they expire
  useEffect(() => {
    if (!tokens) return;

    const expiresAt = Date.now() + (tokens.expires_in * 1000);
    const refreshTime = expiresAt - (5 * 60 * 1000); // Refresh 5 minutes before expiry
    const timeUntilRefresh = refreshTime - Date.now();

    if (timeUntilRefresh > 0) {
      const timeout = setTimeout(() => {
        refreshTokens();
      }, timeUntilRefresh);

      return () => clearTimeout(timeout);
    } else {
      // Token already expired, try to refresh immediately
      refreshTokens();
    }
  }, [tokens]);

  const makeAuthenticatedRequest = async (
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> => {
    const headers = {
      'Content-Type': 'application/json',
      ...(tokens && { Authorization: `Bearer ${tokens.access_token}` }),
      ...options.headers,
    };

    const response = await fetch(`${API_BASE}${url}`, {
      ...options,
      headers,
    });

    // If unauthorized and we have a refresh token, try to refresh
    if (response.status === 401 && tokens?.refresh_token) {
      const refreshSuccess = await refreshTokens();
      if (refreshSuccess) {
        // Retry the request with new token
        const newHeaders = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${tokens.access_token}`,
          ...options.headers,
        };
        return fetch(`${API_BASE}${url}`, {
          ...options,
          headers: newHeaders,
        });
      }
    }

    return response;
  };

  const login = async (username: string, password: string): Promise<void> => {
    try {
      setIsLoading(true);
      console.log('AuthContext: Starting login for', username);
      
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      console.log('AuthContext: Making request to', `${API_BASE}/api/v1/auth/login`);
      const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('AuthContext: Login failed', error);
        throw new Error(error.detail || 'Login failed');
      }

      const authTokens: AuthTokens = await response.json();
      console.log('AuthContext: Got tokens', authTokens);
      setTokens(authTokens);
      setStoredTokens(authTokens);

      // Fetch user profile
      console.log('AuthContext: Fetching user profile');
      const userResponse = await fetch(`${API_BASE}/api/v1/auth/me`, {
        headers: {
          Authorization: `Bearer ${authTokens.access_token}`,
        },
      });

      if (!userResponse.ok) {
        console.error('AuthContext: Failed to fetch user profile');
        throw new Error('Failed to fetch user profile');
      }

      const userData: User = await userResponse.json();
      console.log('AuthContext: Got user data', userData);
      setUser(userData);
      setStoredUser(userData);
      console.log('AuthContext: Login complete, isAuthenticated should be true');
    } catch (error) {
      console.error('AuthContext: Login error', error);
      // Clear any stored data on login failure
      logout();
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<void> => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE}/api/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Registration failed');
      }

      // Auto-login after successful registration
      await login(userData.username, userData.password);
    } catch (error) {
      logout();
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshTokens = useCallback(async (): Promise<boolean> => {
    try {
      if (!tokens?.refresh_token) {
        logout();
        return false;
      }

      const response = await fetch(`${API_BASE}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: tokens.refresh_token }),
      });

      if (!response.ok) {
        logout();
        return false;
      }

      const newTokens: AuthTokens = await response.json();
      setTokens(newTokens);
      setStoredTokens(newTokens);
      
      setIsLoading(false);
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
      return false;
    }
  }, [tokens?.refresh_token]);

  const logout = useCallback(() => {
    setUser(null);
    setTokens(null);
    setStoredUser(null);
    setStoredTokens(null);
    setIsLoading(false);
  }, []);

  const updateProfile = async (data: Partial<User>): Promise<void> => {
    try {
      const response = await makeAuthenticatedRequest('/api/v1/users/me', {
        method: 'PUT',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Profile update failed');
      }

      const updatedUser: User = await response.json();
      setUser(updatedUser);
      setStoredUser(updatedUser);
    } catch (error) {
      throw error;
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      const response = await makeAuthenticatedRequest('/api/v1/auth/change-password', {
        method: 'POST',
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Password change failed');
      }
    } catch (error) {
      throw error;
    }
  };

  const value = {
    user,
    tokens,
    isLoading,
    isAuthenticated: !!user && !!tokens,
    login,
    register,
    logout,
    refreshTokens,
    updateProfile,
    changePassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}