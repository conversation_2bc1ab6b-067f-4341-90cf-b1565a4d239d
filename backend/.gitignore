# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Environment variables
.env
.env.*

# FastAPI-specific
instance/
*.log

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
coverage.xml
*.cover
.hypothesis/

# Jupyter
.ipynb_checkpoints

# VS Code / PyCharm
.vscode/
.idea/
*.code-workspace

# MyPy / Pyre
.mypy_cache/
.pyre/

# Pytest
.pytest_cache/

# Build artifacts
build/
dist/
*.egg-info/

# SQLite / DB files
*.sqlite3
*.db

# OS-specific
.DS_Store
Thumbs.db

# uv package manager (cache and virtual env)
.venv/

# ruff
.ruff_cache/
