from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from typing import List, Optional, Tuple
import time

from ..models.database import Capture, Frame, SyncStatus
from ..schemas.frame import CaptureCreate, CaptureUpdate, CaptureResponse
from .file_storage_service import create_file_storage_service, ThumbnailSize


class CaptureService:
    """Service layer for capture operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.storage_service = create_file_storage_service(db)
    
    def create_capture(self, capture_data: CaptureCreate) -> Capture:
        """
        Create a new capture with frame existence validation
        """
        # Validate frame exists
        frame = self.db.query(Frame).filter(Frame.frame_id == capture_data.frame_id).first()
        if not frame:
            raise ValueError(f"Frame with ID {capture_data.frame_id} not found")
        
        # Create capture instance
        current_timestamp = int(time.time())
        capture = Capture(
            frame_id=capture_data.frame_id,
            capture_timestamp=current_timestamp,
            detection_results=[result.dict() for result in capture_data.detection_results],
            sync_status=SyncStatus.SYNCED,
            sync_version=1
        )
        
        # Store images using the storage service
        if capture_data.original_image_data:
            # Store original image with compression
            original_result = self.storage_service.store_image(
                capture_data.original_image_data, 
                image_type="original",
                compress=True,
                quality=85
            )
            capture.original_image_blob = original_result["data"]
        
        if capture_data.processed_image_data:
            # Store processed image with compression
            processed_result = self.storage_service.store_image(
                capture_data.processed_image_data,
                image_type="processed", 
                compress=True,
                quality=85
            )
            capture.processed_image_blob = processed_result["data"]
        
        # Generate thumbnail if original image provided
        if capture_data.original_image_data:
            thumbnails = self.storage_service.generate_thumbnails(
                capture_data.original_image_data,
                sizes=[ThumbnailSize.SMALL]  # Generate small thumbnail for list views
            )
            if ThumbnailSize.SMALL.value in thumbnails:
                capture.thumbnail_blob = thumbnails[ThumbnailSize.SMALL.value]
        
        # Add to database
        self.db.add(capture)
        
        # Update frame capture count
        frame.capture_count += 1
        frame.last_modified_timestamp = current_timestamp
        
        self.db.commit()
        self.db.refresh(capture)
        
        return capture
    
    def get_capture(self, capture_id: str) -> Optional[Capture]:
        """
        Retrieve a capture by ID with proper error handling
        """
        try:
            capture = self.db.query(Capture).filter(Capture.capture_id == capture_id).first()
            return capture
        except Exception as e:
            raise RuntimeError(f"Error retrieving capture {capture_id}: {str(e)}")
    
    def update_capture(self, capture_id: str, capture_update: CaptureUpdate, expected_version: Optional[int] = None) -> Capture:
        """
        Update a capture with optimistic locking using sync_version
        """
        capture = self.get_capture(capture_id)
        if not capture:
            raise ValueError(f"Capture with ID {capture_id} not found")
        
        # Optimistic locking check
        if expected_version is not None and capture.sync_version != expected_version:
            raise ValueError(
                f"Capture version conflict. Expected {expected_version}, found {capture.sync_version}. "
                "Capture may have been modified by another process."
            )
        
        # Update fields if provided
        if capture_update.detection_results is not None:
            capture.detection_results = [result.dict() for result in capture_update.detection_results]
        
        if capture_update.processed_image_data is not None:
            capture.processed_image_blob = capture_update.processed_image_data
        
        # Increment sync version and update timestamp
        capture.sync_version += 1
        capture.sync_status = SyncStatus.SYNCED
        
        # Update frame's last modified timestamp
        frame = self.db.query(Frame).filter(Frame.frame_id == capture.frame_id).first()
        if frame:
            frame.last_modified_timestamp = int(time.time())
        
        self.db.commit()
        self.db.refresh(capture)
        
        return capture
    
    def delete_capture(self, capture_id: str) -> bool:
        """
        Delete a capture with cascade considerations and file cleanup
        """
        capture = self.get_capture(capture_id)
        if not capture:
            return False
        
        frame_id = capture.frame_id
        
        try:
            # Clean up stored images using storage service
            self.storage_service.delete_images(capture_id)
            
            # Delete the capture record
            self.db.delete(capture)
            
            # Update frame capture count
            frame = self.db.query(Frame).filter(Frame.frame_id == frame_id).first()
            if frame:
                frame.capture_count = max(0, frame.capture_count - 1)
                frame.last_modified_timestamp = int(time.time())
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            raise RuntimeError(f"Failed to delete capture {capture_id}: {str(e)}")
    
    def get_captures_by_frame(
        self, 
        frame_id: str, 
        page: int = 1, 
        limit: int = 20, 
        sort_by: str = "capture_timestamp", 
        sort_order: str = "desc"
    ) -> Tuple[List[Capture], int]:
        """
        Get captures for a frame with pagination and sorting
        """
        # Validate frame exists
        frame = self.db.query(Frame).filter(Frame.frame_id == frame_id).first()
        if not frame:
            raise ValueError(f"Frame with ID {frame_id} not found")
        
        # Build query
        query = self.db.query(Capture).filter(Capture.frame_id == frame_id)
        
        # Apply sorting
        sort_column = getattr(Capture, sort_by, Capture.capture_timestamp)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * limit
        captures = query.offset(offset).limit(limit).all()
        
        return captures, total
    
    def get_capture_count_by_frame(self, frame_id: str) -> int:
        """
        Get the total number of captures for a frame
        """
        return self.db.query(Capture).filter(Capture.frame_id == frame_id).count()
    
    def get_capture_storage_info(self, capture_id: str) -> dict:
        """
        Get storage information for a capture
        """
        return self.storage_service.get_storage_info(capture_id)
    
    def get_storage_metrics(self) -> dict:
        """
        Get storage service metrics
        """
        return self.storage_service.get_metrics()
    
    def storage_health_check(self) -> dict:
        """
        Perform storage system health check
        """
        return self.storage_service.health_check()
    
    def regenerate_thumbnails(self, capture_id: str, sizes: List[ThumbnailSize] = None) -> bool:
        """
        Regenerate thumbnails for a capture
        """
        try:
            capture = self.get_capture(capture_id)
            if not capture or not capture.original_image_blob:
                return False
            
            thumbnails = self.storage_service.generate_thumbnails(
                capture.original_image_blob, sizes
            )
            
            # Update the thumbnail blob with the small size
            if ThumbnailSize.SMALL.value in thumbnails:
                capture.thumbnail_blob = thumbnails[ThumbnailSize.SMALL.value]
                self.db.commit()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise RuntimeError(f"Failed to regenerate thumbnails: {str(e)}")
    
    def update_frame_capture_count(self, frame_id: str) -> None:
        """
        Recalculate and update the capture count for a frame
        """
        frame = self.db.query(Frame).filter(Frame.frame_id == frame_id).first()
        if not frame:
            raise ValueError(f"Frame with ID {frame_id} not found")
        
        # Count actual captures
        actual_count = self.db.query(Capture).filter(Capture.frame_id == frame_id).count()
        
        # Update frame
        frame.capture_count = actual_count
        frame.last_modified_timestamp = int(time.time())
        
        self.db.commit()
    
    def get_captures_by_sync_status(self, sync_status: SyncStatus, limit: int = 100) -> List[Capture]:
        """
        Get captures by sync status (useful for sync operations)
        """
        return (
            self.db.query(Capture)
            .filter(Capture.sync_status == sync_status)
            .limit(limit)
            .all()
        )