from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, update, delete
from sqlalchemy.orm import selectinload
from typing import List, Optional
import time
import uuid

from ..models.database import Frame, Capture
from ..schemas.frame import FrameCreate, FrameUpdate, FrameResponse, FrameListResponse


class FrameService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_frame(self, frame_data: FrameCreate) -> FrameResponse:
        """Create a new frame session."""
        current_time = int(time.time() * 1000)  # Milliseconds timestamp
        
        db_frame = Frame(
            frame_id=str(uuid.uuid4()),
            model_number=frame_data.model_number,
            machine_serial_number=frame_data.machine_serial_number,
            inspector_name=frame_data.inspector_name,
            creation_timestamp=current_time,
            last_modified_timestamp=current_time,
            status=frame_data.status,
            frame_metadata=frame_data.metadata,
            capture_count=0
        )
        
        self.db.add(db_frame)
        await self.db.commit()
        await self.db.refresh(db_frame)
        
        return FrameResponse.model_validate(db_frame)
    
    async def get_frame(self, frame_id: str) -> Optional[FrameResponse]:
        """Get a specific frame by ID."""
        result = await self.db.execute(
            select(Frame).where(Frame.frame_id == frame_id)
        )
        frame = result.scalar_one_or_none()
        
        if frame:
            return FrameResponse.model_validate(frame)
        return None
    
    async def get_frames(
        self, 
        skip: int = 0, 
        limit: int = 100,
        inspector_name: Optional[str] = None,
        machine_serial_number: Optional[str] = None,
        status: Optional[str] = None
    ) -> FrameListResponse:
        """Get list of frames with optional filtering."""
        query = select(Frame)
        
        # Add filters
        if inspector_name:
            query = query.where(Frame.inspector_name == inspector_name)
        if machine_serial_number:
            query = query.where(Frame.machine_serial_number == machine_serial_number)
        if status:
            query = query.where(Frame.status == status)
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # Add pagination and ordering
        query = query.order_by(Frame.last_modified_timestamp.desc()).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        frames = result.scalars().all()
        
        return FrameListResponse(
            frames=[FrameResponse.model_validate(frame) for frame in frames],
            total=total,
            page=skip // limit + 1,
            limit=limit
        )
    
    async def update_frame(self, frame_id: str, frame_data: FrameUpdate) -> Optional[FrameResponse]:
        """Update an existing frame."""
        # Check if frame exists
        result = await self.db.execute(
            select(Frame).where(Frame.frame_id == frame_id)
        )
        frame = result.scalar_one_or_none()
        
        if not frame:
            return None
        
        # Update fields that are provided
        update_data = frame_data.dict(exclude_unset=True)
        if update_data:
            update_data['last_modified_timestamp'] = int(time.time() * 1000)
            
            await self.db.execute(
                update(Frame)
                .where(Frame.frame_id == frame_id)
                .values(**update_data)
            )
            await self.db.commit()
            
            # Refresh the frame
            await self.db.refresh(frame)
        
        return FrameResponse.model_validate(frame)
    
    async def delete_frame(self, frame_id: str) -> bool:
        """Delete a frame and all its captures."""
        result = await self.db.execute(
            select(Frame).where(Frame.frame_id == frame_id)
        )
        frame = result.scalar_one_or_none()
        
        if not frame:
            return False
        
        await self.db.delete(frame)
        await self.db.commit()
        return True
    
    async def get_frame_with_captures(self, frame_id: str) -> Optional[dict]:
        """Get frame with all its captures."""
        result = await self.db.execute(
            select(Frame)
            .options(selectinload(Frame.captures))
            .where(Frame.frame_id == frame_id)
        )
        frame = result.scalar_one_or_none()
        
        if not frame:
            return None
        
        return {
            "frame": FrameResponse.model_validate(frame),
            "captures": [
                {
                    "capture_id": capture.capture_id,
                    "capture_timestamp": capture.capture_timestamp,
                    "detection_results": capture.detection_results,
                    "sync_status": capture.sync_status.value
                }
                for capture in frame.captures
            ]
        }