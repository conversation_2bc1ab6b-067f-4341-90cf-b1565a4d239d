from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from ..database import get_db
from ..services.frame_service import FrameService
from ..schemas.frame import FrameCreate, FrameUpdate, FrameResponse, FrameListResponse
from ..dependencies import get_current_user

router = APIRouter(prefix="/api/v1/frames", tags=["frames"])


@router.post("/", response_model=FrameResponse)
async def create_frame(
    frame_data: FrameCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new frame session."""
    frame_service = FrameService(db)
    return await frame_service.create_frame(frame_data)


@router.get("/", response_model=FrameListResponse)
async def get_frames(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    inspector_name: Optional[str] = Query(None, description="Filter by inspector name"),
    machine_serial_number: Optional[str] = Query(None, description="Filter by machine serial number"),
    status: Optional[str] = Query(None, description="Filter by status (active, completed, archived)"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get list of frames with optional filtering and pagination."""
    frame_service = FrameService(db)
    return await frame_service.get_frames(
        skip=skip, 
        limit=limit,
        inspector_name=inspector_name,
        machine_serial_number=machine_serial_number,
        status=status
    )


@router.get("/{frame_id}", response_model=FrameResponse)
async def get_frame(
    frame_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get a specific frame by ID."""
    frame_service = FrameService(db)
    frame = await frame_service.get_frame(frame_id)
    
    if not frame:
        raise HTTPException(status_code=404, detail="Frame not found")
    
    return frame


@router.put("/{frame_id}", response_model=FrameResponse)
async def update_frame(
    frame_id: str,
    frame_data: FrameUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update an existing frame."""
    frame_service = FrameService(db)
    frame = await frame_service.update_frame(frame_id, frame_data)
    
    if not frame:
        raise HTTPException(status_code=404, detail="Frame not found")
    
    return frame


@router.delete("/{frame_id}")
async def delete_frame(
    frame_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Delete a frame and all its captures."""
    frame_service = FrameService(db)
    success = await frame_service.delete_frame(frame_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Frame not found")
    
    return {"message": "Frame deleted successfully"}


@router.get("/{frame_id}/details")
async def get_frame_with_captures(
    frame_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get frame with all its captures."""
    frame_service = FrameService(db)
    result = await frame_service.get_frame_with_captures(frame_id)
    
    if not result:
        raise HTTPException(status_code=404, detail="Frame not found")
    
    return result


@router.get("/search/")
async def search_frames(
    q: str = Query(..., min_length=1, description="Search query"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Search frames by model number, machine serial, or inspector name."""
    frame_service = FrameService(db)
    
    # Search in multiple fields - this is a simple implementation
    # In production, you might want to use full-text search
    results = await frame_service.get_frames(skip=skip, limit=limit)
    
    # Filter results based on search query
    filtered_frames = [
        frame for frame in results.frames
        if (q.lower() in frame.model_number.lower() or
            q.lower() in frame.machine_serial_number.lower() or
            q.lower() in frame.inspector_name.lower())
    ]
    
    return FrameListResponse(
        frames=filtered_frames,
        total=len(filtered_frames),
        page=skip // limit + 1,
        limit=limit
    )