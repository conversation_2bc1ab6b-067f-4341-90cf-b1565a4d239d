from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..dependencies import get_current_user
from ..services.capture_service import CaptureService
from ..schemas.frame import (
    CaptureCreate, 
    CaptureUpdate, 
    CaptureResponse, 
    CaptureListResponse
)
from ..models.database import SyncStatus

router = APIRouter(
    prefix="/api/v1/captures",
    tags=["captures"],
    dependencies=[Depends(get_current_user)]  # Require authentication for all endpoints
)


@router.post("/", response_model=CaptureResponse, status_code=status.HTTP_201_CREATED)
async def create_capture(
    capture_data: CaptureCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new capture
    
    - **frame_id**: ID of the frame this capture belongs to
    - **detection_results**: List of detected objects in the image
    - **original_image_data**: Original image blob data (optional)
    - **processed_image_data**: Processed image with annotations (optional)
    """
    try:
        capture_service = CaptureService(db)
        capture = capture_service.create_capture(capture_data)
        return CaptureResponse.model_validate(capture)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create capture: {str(e)}"
        )


@router.get("/{capture_id}", response_model=CaptureResponse)
async def get_capture(
    capture_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Retrieve a specific capture by ID
    
    - **capture_id**: The unique identifier of the capture
    """
    try:
        capture_service = CaptureService(db)
        capture = capture_service.get_capture(capture_id)
        
        if not capture:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Capture with ID {capture_id} not found"
            )
        
        return CaptureResponse.model_validate(capture)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve capture: {str(e)}"
        )


@router.put("/{capture_id}", response_model=CaptureResponse)
async def update_capture(
    capture_id: str,
    capture_update: CaptureUpdate,
    sync_version: Optional[int] = Query(None, description="Expected sync version for optimistic locking"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Update an existing capture
    
    - **capture_id**: The unique identifier of the capture
    - **detection_results**: Updated detection results (optional)
    - **processed_image_data**: Updated processed image (optional)
    - **sync_version**: Expected version for optimistic locking (optional)
    """
    try:
        capture_service = CaptureService(db)
        capture = capture_service.update_capture(capture_id, capture_update, sync_version)
        return CaptureResponse.model_validate(capture)
        
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "version conflict" in error_msg or "modified by another process" in error_msg:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=error_msg
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update capture: {str(e)}"
        )


@router.delete("/{capture_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_capture(
    capture_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Delete a capture
    
    - **capture_id**: The unique identifier of the capture to delete
    """
    try:
        capture_service = CaptureService(db)
        deleted = capture_service.delete_capture(capture_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Capture with ID {capture_id} not found"
            )
        
        return None  # 204 No Content
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete capture: {str(e)}"
        )


# Frame captures endpoint (moved from frames router for better organization)
@router.get("/frames/{frame_id}/captures", response_model=CaptureListResponse)
async def get_frame_captures(
    frame_id: str,
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    limit: int = Query(20, ge=1, le=100, description="Number of captures per page"),
    sort_by: str = Query("capture_timestamp", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order (asc/desc)"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get all captures for a specific frame with pagination
    
    - **frame_id**: The unique identifier of the frame
    - **page**: Page number (default: 1)
    - **limit**: Number of captures per page (default: 20, max: 100)
    - **sort_by**: Field to sort by (default: capture_timestamp)
    - **sort_order**: Sort order - asc or desc (default: desc)
    """
    try:
        capture_service = CaptureService(db)
        captures, total = capture_service.get_captures_by_frame(
            frame_id=frame_id,
            page=page,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        capture_responses = [CaptureResponse.model_validate(capture) for capture in captures]
        
        return CaptureListResponse(
            captures=capture_responses,
            total=total,
            page=page,
            limit=limit
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve frame captures: {str(e)}"
        )


# Alternative endpoint with different path structure
@router.get("/", response_model=CaptureListResponse)
async def list_captures(
    frame_id: Optional[str] = Query(None, description="Filter by frame ID"),
    sync_status: Optional[str] = Query(None, description="Filter by sync status"),
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    limit: int = Query(20, ge=1, le=100, description="Number of captures per page"),
    sort_by: str = Query("capture_timestamp", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order (asc/desc)"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    List captures with optional filtering
    
    - **frame_id**: Filter captures by frame ID (optional)
    - **sync_status**: Filter by sync status (optional)
    - **page**: Page number (default: 1)
    - **limit**: Number of captures per page (default: 20, max: 100)
    - **sort_by**: Field to sort by (default: capture_timestamp)
    - **sort_order**: Sort order - asc or desc (default: desc)
    """
    try:
        capture_service = CaptureService(db)
        
        if frame_id:
            # Use frame-specific method if frame_id provided
            captures, total = capture_service.get_captures_by_frame(
                frame_id=frame_id,
                page=page,
                limit=limit,
                sort_by=sort_by,
                sort_order=sort_order
            )
        elif sync_status:
            # Filter by sync status
            try:
                status_enum = SyncStatus(sync_status)
                captures = capture_service.get_captures_by_sync_status(status_enum, limit * page)
                # For sync status filtering, we return all matches (no pagination for now)
                total = len(captures)
                # Apply manual pagination
                start_idx = (page - 1) * limit
                end_idx = start_idx + limit
                captures = captures[start_idx:end_idx]
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid sync_status: {sync_status}. Valid values: synced, pending, conflict"
                )
        else:
            # Return empty list if no filters provided (to prevent returning all captures)
            captures = []
            total = 0
        
        capture_responses = [CaptureResponse.model_validate(capture) for capture in captures]
        
        return CaptureListResponse(
            captures=capture_responses,
            total=total,
            page=page,
            limit=limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list captures: {str(e)}"
        )


# Storage management endpoints
@router.get("/{capture_id}/storage", response_model=dict)
async def get_capture_storage_info(
    capture_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get storage information for a capture
    
    - **capture_id**: The unique identifier of the capture
    """
    try:
        capture_service = CaptureService(db)
        storage_info = capture_service.get_capture_storage_info(capture_id)
        
        if "error" in storage_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=storage_info["error"]
            )
        
        return storage_info
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get storage info: {str(e)}"
        )


@router.post("/{capture_id}/thumbnails", status_code=status.HTTP_200_OK)
async def regenerate_thumbnails(
    capture_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Regenerate thumbnails for a capture
    
    - **capture_id**: The unique identifier of the capture
    """
    try:
        capture_service = CaptureService(db)
        success = capture_service.regenerate_thumbnails(capture_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Capture {capture_id} not found or has no original image"
            )
        
        return {"message": "Thumbnails regenerated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate thumbnails: {str(e)}"
        )


@router.get("/storage/metrics", response_model=dict)
async def get_storage_metrics(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get storage service metrics
    """
    try:
        capture_service = CaptureService(db)
        metrics = capture_service.get_storage_metrics()
        return metrics
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get storage metrics: {str(e)}"
        )


# Health check endpoint for captures
@router.get("/health", status_code=status.HTTP_200_OK)
async def captures_health_check(db: Session = Depends(get_db)):
    """
    Health check endpoint for captures service including storage
    """
    try:
        capture_service = CaptureService(db)
        storage_health = capture_service.storage_health_check()
        
        return {
            "status": "healthy",
            "service": "captures",
            "storage": storage_health
        }
        
    except Exception as e:
        return {
            "status": "unhealthy", 
            "service": "captures",
            "error": str(e)
        }