"""
Configuration settings for the weld defect detection system
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Database settings
    database_url: str = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./weld_detection.db")
    
    # Storage settings (Option A: Database storage)
    storage_type: str = "database"  # Options: "database", "filesystem", "cloud"
    
    # Image processing settings
    image_compression_enabled: bool = True
    image_compression_quality: int = 85
    max_image_size_mb: int = 10
    
    # Thumbnail settings
    thumbnail_small_size: int = 150
    thumbnail_medium_size: int = 300
    thumbnail_large_size: int = 600
    
    # Performance settings
    enable_storage_metrics: bool = True
    storage_health_check_interval: int = 300  # seconds
    
    # Security settings
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # API settings
    api_title: str = "Weld Defect Detection API"
    api_version: str = "1.0.0"
    api_description: str = "Backend API for weld defect detection system"
    
    # CORS settings
    allowed_origins: list = ["http://localhost:3000", "http://localhost:3001"]
    
    class Config:
        env_file = ".env"


# Global settings instance
settings = Settings()