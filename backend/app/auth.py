from datetime import datetime, timedelta
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
import os
import secrets

from .models.database import User, UserRole
from .utils.password import verify_password, get_password_hash

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
REFRESH_SECRET_KEY = os.getenv("REFRESH_SECRET_KEY", "your-refresh-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7
RESET_TOKEN_EXPIRE_MINUTES = 60


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT refresh token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, REFRESH_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_reset_token(email: str) -> str:
    """Create password reset token."""
    expire = datetime.utcnow() + timedelta(minutes=RESET_TOKEN_EXPIRE_MINUTES)
    to_encode = {"email": email, "exp": expire, "type": "reset", "nonce": secrets.token_urlsafe(16)}
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """Verify JWT access token and return payload."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        if username is None or token_type != "access":
            raise credentials_exception
        return {"username": username, "payload": payload}
    except JWTError:
        raise credentials_exception


def verify_refresh_token(token: str) -> dict:
    """Verify JWT refresh token and return payload."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid refresh token",
    )
    
    try:
        payload = jwt.decode(token, REFRESH_SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        if username is None or token_type != "refresh":
            raise credentials_exception
        return {"username": username, "payload": payload}
    except JWTError:
        raise credentials_exception


def verify_reset_token(token: str) -> dict:
    """Verify password reset token and return payload."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Invalid or expired reset token",
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("email")
        token_type: str = payload.get("type")
        if email is None or token_type != "reset":
            raise credentials_exception
        return {"email": email, "payload": payload}
    except JWTError:
        raise credentials_exception


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """Authenticate user credentials."""
    from .repositories.user_repository import UserRepository
    user_repo = UserRepository(db)
    user = await user_repo.get_user_by_username(username)
    
    if not user:
        return None
    if not user.is_active:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    
    # Update last login timestamp
    await user_repo.update_last_login(user.user_id)
    return user


async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
    """Get user by username."""
    from .repositories.user_repository import UserRepository
    user_repo = UserRepository(db)
    return await user_repo.get_user_by_username(username)


async def get_user_by_id(db: AsyncSession, user_id: str) -> Optional[User]:
    """Get user by ID."""
    from .repositories.user_repository import UserRepository
    user_repo = UserRepository(db)
    return await user_repo.get_user_by_id(user_id)


async def create_default_users(db: AsyncSession) -> None:
    """Create default users if they don't exist."""
    from .repositories.user_repository import UserRepository
    user_repo = UserRepository(db)
    
    # Check if admin user exists
    admin_user = await user_repo.get_user_by_username("admin")
    if not admin_user:
        await user_repo.create_user(
            username="admin",
            email="<EMAIL>",
            full_name="System Admin",
            password="admin123",
            role=UserRole.ADMIN
        )
    
    # Check if inspector user exists
    inspector_user = await user_repo.get_user_by_username("inspector1")
    if not inspector_user:
        await user_repo.create_user(
            username="inspector1",
            email="<EMAIL>",
            full_name="Inspector One",
            password="password123",
            role=UserRole.INSPECTOR
        )