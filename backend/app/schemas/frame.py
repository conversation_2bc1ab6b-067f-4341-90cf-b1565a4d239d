from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
import time

class DetectionResult(BaseModel):
    id: str
    class_name: str = Field(alias="class")
    confidence: float
    bbox: Dict[str, float]  # {x1, y1, x2, y2}

    class Config:
        validate_by_name = True


class FrameBase(BaseModel):
    model_number: str
    machine_serial_number: str
    inspector_name: str
    status: str = "active"
    metadata: Optional[Dict[str, Any]] = None


class FrameCreate(FrameBase):
    pass


class FrameUpdate(BaseModel):
    model_number: Optional[str] = None
    machine_serial_number: Optional[str] = None
    inspector_name: Optional[str] = None
    status: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class FrameResponse(FrameBase):
    frame_id: str
    creation_timestamp: int
    last_modified_timestamp: int
    capture_count: int
    sync_status: str = "synced"
    last_synced_at: Optional[int] = None

    @classmethod
    def model_validate(cls, obj):
        # Map frame_metadata back to metadata for the response
        if hasattr(obj, 'frame_metadata'):
            obj.metadata = obj.frame_metadata
        return super().model_validate(obj)

    class Config:
        from_attributes = True


class CaptureBase(BaseModel):
    frame_id: str
    detection_results: List[DetectionResult] = []


class CaptureCreate(CaptureBase):
    """Schema for creating a new capture"""
    original_image_data: Optional[bytes] = None
    processed_image_data: Optional[bytes] = None
    
    @validator('detection_results')
    def validate_detection_results(cls, v):
        """Validate detection results structure"""
        if len(v) > 100:  # Max 100 detections per capture
            raise ValueError("Too many detection results (max 100)")
        return v
    
    @validator('original_image_data', 'processed_image_data')
    def validate_image_size(cls, v):
        """Validate image blob size (max 10MB per image)"""
        if v is not None and len(v) > 10 * 1024 * 1024:  # 10MB limit
            raise ValueError("Image size exceeds 10MB limit")
        return v


class CaptureUpdate(BaseModel):
    """Schema for updating an existing capture"""
    detection_results: Optional[List[DetectionResult]] = None
    processed_image_data: Optional[bytes] = None
    
    @validator('detection_results')
    def validate_detection_results(cls, v):
        """Validate detection results structure"""
        if v is not None and len(v) > 100:  # Max 100 detections per capture
            raise ValueError("Too many detection results (max 100)")
        return v
    
    @validator('processed_image_data')
    def validate_image_size(cls, v):
        """Validate processed image blob size (max 10MB)"""
        if v is not None and len(v) > 10 * 1024 * 1024:  # 10MB limit
            raise ValueError("Image size exceeds 10MB limit")
        return v


class CaptureResponse(CaptureBase):
    """Schema for capture response"""
    capture_id: str
    capture_timestamp: int
    sync_status: str = "synced"
    sync_version: int = 1
    last_sync_attempt: Optional[int] = None
    has_original_image: bool = False
    has_processed_image: bool = False
    has_thumbnail: bool = False
    image_sizes: Optional[Dict[str, int]] = None  # Size info for images

    class Config:
        from_attributes = True
        
    @classmethod
    def model_validate(cls, obj):
        """Custom validation to handle image presence flags"""
        if hasattr(obj, 'original_image_blob'):
            obj.has_original_image = obj.original_image_blob is not None
        if hasattr(obj, 'processed_image_blob'):
            obj.has_processed_image = obj.processed_image_blob is not None
        if hasattr(obj, 'thumbnail_blob'):
            obj.has_thumbnail = obj.thumbnail_blob is not None
        
        # Calculate image sizes if available
        sizes = {}
        if hasattr(obj, 'original_image_blob') and obj.original_image_blob:
            sizes['original'] = len(obj.original_image_blob)
        if hasattr(obj, 'processed_image_blob') and obj.processed_image_blob:
            sizes['processed'] = len(obj.processed_image_blob)
        if hasattr(obj, 'thumbnail_blob') and obj.thumbnail_blob:
            sizes['thumbnail'] = len(obj.thumbnail_blob)
        obj.image_sizes = sizes if sizes else None
        
        return super().model_validate(obj)


class CaptureListResponse(BaseModel):
    """Schema for paginated capture list response"""
    captures: List[CaptureResponse]
    total: int
    page: int
    limit: int
    has_next: bool = False
    has_previous: bool = False
    
    @validator('has_next', always=True)
    def set_has_next(cls, v, values):
        """Calculate if there are more pages"""
        total = values.get('total', 0)
        page = values.get('page', 1)
        limit = values.get('limit', 10)
        return (page * limit) < total
    
    @validator('has_previous', always=True)
    def set_has_previous(cls, v, values):
        """Calculate if there are previous pages"""
        page = values.get('page', 1)
        return page > 1


class FrameListResponse(BaseModel):
    frames: List[FrameResponse]
    total: int
    page: int
    limit: int