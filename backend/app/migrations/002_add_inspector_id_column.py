"""
Migration script to add inspector_id column to frames table
"""

from sqlalchemy import text
from sqlalchemy.orm import Session
from ..database import engine


def upgrade():
    """Add inspector_id column to frames table"""
    
    with Session(engine) as session:
        try:
            # Check if inspector_id column already exists
            result = session.execute(text("""
                PRAGMA table_info(frames);
            """)).fetchall()
            
            columns = [row[1] for row in result]  # Column names are in index 1
            
            if 'inspector_id' not in columns:
                print("Adding inspector_id column to frames table...")
                
                # Add inspector_id column (nullable initially)
                session.execute(text("""
                    ALTER TABLE frames 
                    ADD COLUMN inspector_id TEXT;
                """))
                
                # Set default inspector_id based on inspector_name
                # First, try to match with existing users
                session.execute(text("""
                    UPDATE frames 
                    SET inspector_id = (
                        SELECT user_id FROM users 
                        WHERE users.username = frames.inspector_name 
                        OR users.full_name = frames.inspector_name
                        LIMIT 1
                    )
                    WHERE inspector_id IS NULL;
                """))
                
                # For any remaining frames without inspector_id, use admin user
                session.execute(text("""
                    UPDATE frames 
                    SET inspector_id = (
                        SELECT user_id FROM users WHERE role = 'admin' LIMIT 1
                    )
                    WHERE inspector_id IS NULL;
                """))
                
                # Create foreign key constraint (note: SQLite doesn't support adding foreign key constraints to existing tables easily)
                # We'll handle this through application-level validation for now
                
                print("✅ Inspector ID column added successfully")
            else:
                print("✅ Inspector ID column already exists, skipping...")
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error adding inspector_id column: {e}")
            raise


def downgrade():
    """Remove inspector_id column from frames table"""
    
    with Session(engine) as session:
        try:
            # SQLite doesn't support DROP COLUMN directly
            # We would need to recreate the table, but that's complex
            # For now, just set the column to NULL
            session.execute(text("""
                UPDATE frames SET inspector_id = NULL;
            """))
            
            session.commit()
            print("✅ Inspector ID column values cleared (column still exists)")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error clearing inspector_id column: {e}")
            raise


if __name__ == "__main__":
    print("Running inspector_id migration...")
    upgrade()