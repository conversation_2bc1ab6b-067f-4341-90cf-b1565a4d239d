import pytest
import time
import json
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.models.database import Base, Frame, FrameStatus
from app.database import get_db
from app.dependencies import get_current_user


# Test database setup
TEST_DATABASE_URL = "sqlite:///./test_capture_api.db" 
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


def override_get_current_user():
    """Override authentication for testing"""
    return {"user_id": "test_user", "username": "testuser"}


# Override dependencies
app.dependency_overrides[get_db] = override_get_db
app.dependency_overrides[get_current_user] = override_get_current_user

client = TestClient(app)


@pytest.fixture(scope="function")
def test_db_setup():
    """Setup test database"""
    Base.metadata.create_all(bind=test_engine)
    yield
    Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def sample_frame():
    """Create a sample frame for testing"""
    db = TestingSessionLocal()
    try:
        frame = Frame(
            frame_id="test-frame-api-123",
            model_number="MODEL-API-001",
            machine_serial_number="MACHINE-API-001",
            inspector_name="API Test Inspector", 
            creation_timestamp=int(time.time()),
            last_modified_timestamp=int(time.time()),
            status=FrameStatus.ACTIVE,
            capture_count=0
        )
        db.add(frame)
        db.commit()
        db.refresh(frame)
        return frame
    finally:
        db.close()


@pytest.fixture
def sample_capture_data():
    """Sample capture data for API tests"""
    return {
        "frame_id": "test-frame-api-123",
        "detection_results": [
            {
                "id": "det-api-1",
                "class": "person",
                "confidence": 0.95,
                "bbox": {"x1": 100, "y1": 100, "x2": 200, "y2": 200}
            },
            {
                "id": "det-api-2", 
                "class": "car",
                "confidence": 0.87,
                "bbox": {"x1": 300, "y1": 150, "x2": 400, "y2": 250}
            }
        ]
    }


class TestCaptureAPI:
    """Integration tests for Capture API endpoints"""
    
    def test_create_capture_success(self, test_db_setup, sample_frame, sample_capture_data):
        """Test successful capture creation via API"""
        response = client.post("/api/v1/captures/", json=sample_capture_data)
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["frame_id"] == sample_capture_data["frame_id"]
        assert len(data["detection_results"]) == 2
        assert data["sync_status"] == "synced"
        assert data["sync_version"] == 1
        assert "capture_id" in data
        assert "capture_timestamp" in data
        
        # Verify boolean flags
        assert data["has_original_image"] is False
        assert data["has_processed_image"] is False
        assert data["has_thumbnail"] is False
    
    def test_create_capture_with_images(self, test_db_setup, sample_frame, sample_capture_data):
        """Test capture creation with image data"""
        # Add image data to the request
        sample_capture_data["original_image_data"] = b"fake_image_data"
        sample_capture_data["processed_image_data"] = b"fake_processed_data"
        
        response = client.post("/api/v1/captures/", json=sample_capture_data)
        
        assert response.status_code == 201
        data = response.json()
        
        # Note: In real implementation, you might need to handle binary data differently
        # This test assumes the API can handle bytes in JSON for testing
        assert data["has_original_image"] is True
        assert data["has_processed_image"] is True
    
    def test_create_capture_invalid_frame(self, test_db_setup, sample_capture_data):
        """Test capture creation with invalid frame ID"""
        sample_capture_data["frame_id"] = "non-existent-frame"
        
        response = client.post("/api/v1/captures/", json=sample_capture_data)
        
        assert response.status_code == 400
        assert "Frame with ID non-existent-frame not found" in response.json()["detail"]
    
    def test_create_capture_validation_error(self, test_db_setup, sample_frame):
        """Test capture creation with validation errors"""
        # Too many detection results
        invalid_data = {
            "frame_id": sample_frame.frame_id,
            "detection_results": [
                {
                    "id": f"det-{i}",
                    "class": "person",
                    "confidence": 0.5,
                    "bbox": {"x1": 0, "y1": 0, "x2": 10, "y2": 10}
                } for i in range(101)  # Over the 100 limit
            ]
        }
        
        response = client.post("/api/v1/captures/", json=invalid_data)
        assert response.status_code == 422  # Validation error
    
    def test_get_capture_success(self, test_db_setup, sample_frame, sample_capture_data):
        """Test successful capture retrieval"""
        # Create a capture first
        create_response = client.post("/api/v1/captures/", json=sample_capture_data)
        assert create_response.status_code == 201
        capture_id = create_response.json()["capture_id"]
        
        # Retrieve the capture
        response = client.get(f"/api/v1/captures/{capture_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["capture_id"] == capture_id
        assert data["frame_id"] == sample_capture_data["frame_id"]
        assert len(data["detection_results"]) == 2
    
    def test_get_capture_not_found(self, test_db_setup):
        """Test capture retrieval with non-existent ID"""
        response = client.get("/api/v1/captures/non-existent-capture")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_update_capture_success(self, test_db_setup, sample_frame, sample_capture_data):
        """Test successful capture update"""
        # Create a capture first
        create_response = client.post("/api/v1/captures/", json=sample_capture_data)
        assert create_response.status_code == 201
        capture_id = create_response.json()["capture_id"]
        
        # Update the capture
        update_data = {
            "detection_results": [
                {
                    "id": "det-updated",
                    "class": "bike",
                    "confidence": 0.75,
                    "bbox": {"x1": 500, "y1": 300, "x2": 600, "y2": 400}
                }
            ]
        }
        
        response = client.put(f"/api/v1/captures/{capture_id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["sync_version"] == 2
        assert len(data["detection_results"]) == 1
        assert data["detection_results"][0]["class"] == "bike"
    
    def test_update_capture_with_version_check(self, test_db_setup, sample_frame, sample_capture_data):
        """Test capture update with optimistic locking"""
        # Create a capture first
        create_response = client.post("/api/v1/captures/", json=sample_capture_data)
        capture_id = create_response.json()["capture_id"]
        
        # Update with correct version
        update_data = {"detection_results": []}
        response = client.put(f"/api/v1/captures/{capture_id}?sync_version=1", json=update_data)
        assert response.status_code == 200
        
        # Try to update with old version (should fail)
        response = client.put(f"/api/v1/captures/{capture_id}?sync_version=1", json=update_data)
        assert response.status_code == 409  # Conflict
        assert "version conflict" in response.json()["detail"]
    
    def test_update_capture_not_found(self, test_db_setup):
        """Test update of non-existent capture"""
        update_data = {"detection_results": []}
        response = client.put("/api/v1/captures/non-existent", json=update_data)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_delete_capture_success(self, test_db_setup, sample_frame, sample_capture_data):
        """Test successful capture deletion"""
        # Create a capture first
        create_response = client.post("/api/v1/captures/", json=sample_capture_data)
        capture_id = create_response.json()["capture_id"]
        
        # Delete the capture
        response = client.delete(f"/api/v1/captures/{capture_id}")
        
        assert response.status_code == 204
        
        # Verify it's gone
        get_response = client.get(f"/api/v1/captures/{capture_id}")
        assert get_response.status_code == 404
    
    def test_delete_capture_not_found(self, test_db_setup):
        """Test deletion of non-existent capture"""
        response = client.delete("/api/v1/captures/non-existent")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_get_frame_captures_success(self, test_db_setup, sample_frame, sample_capture_data):
        """Test getting captures for a frame"""
        frame_id = sample_frame.frame_id
        
        # Create multiple captures
        for i in range(5):
            sample_capture_data["detection_results"][0]["id"] = f"det-{i}"
            response = client.post("/api/v1/captures/", json=sample_capture_data)
            assert response.status_code == 201
            time.sleep(0.01)  # Small delay for different timestamps
        
        # Get captures with pagination
        response = client.get(f"/api/v1/captures/frames/{frame_id}/captures?page=1&limit=3")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 5
        assert len(data["captures"]) == 3
        assert data["page"] == 1
        assert data["limit"] == 3
        assert data["has_next"] is True
        assert data["has_previous"] is False
        
        # Test second page
        response = client.get(f"/api/v1/captures/frames/{frame_id}/captures?page=2&limit=3")
        data = response.json()
        
        assert len(data["captures"]) == 2
        assert data["has_next"] is False
        assert data["has_previous"] is True
    
    def test_get_frame_captures_with_sorting(self, test_db_setup, sample_frame, sample_capture_data):
        """Test frame captures with different sorting"""
        frame_id = sample_frame.frame_id
        
        # Create a few captures
        for i in range(3):
            response = client.post("/api/v1/captures/", json=sample_capture_data)
            assert response.status_code == 201
            time.sleep(0.01)
        
        # Test ascending sort
        response = client.get(
            f"/api/v1/captures/frames/{frame_id}/captures?sort_order=asc&sort_by=capture_timestamp"
        )
        
        assert response.status_code == 200
        data = response.json()
        
        timestamps = [capture["capture_timestamp"] for capture in data["captures"]]
        assert timestamps == sorted(timestamps)  # Should be in ascending order
    
    def test_get_frame_captures_invalid_frame(self, test_db_setup):
        """Test getting captures for non-existent frame"""
        response = client.get("/api/v1/captures/frames/non-existent/captures")
        
        assert response.status_code == 400
        assert "Frame with ID non-existent not found" in response.json()["detail"]
    
    def test_list_captures_with_frame_filter(self, test_db_setup, sample_frame, sample_capture_data):
        """Test listing captures with frame filter"""
        frame_id = sample_frame.frame_id
        
        # Create some captures
        for i in range(3):
            response = client.post("/api/v1/captures/", json=sample_capture_data)
            assert response.status_code == 201
        
        # List with frame filter
        response = client.get(f"/api/v1/captures/?frame_id={frame_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 3
        assert len(data["captures"]) == 3
        assert all(capture["frame_id"] == frame_id for capture in data["captures"])
    
    def test_list_captures_no_filter(self, test_db_setup):
        """Test listing captures without filters (should return empty)"""
        response = client.get("/api/v1/captures/")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return empty when no filters provided
        assert data["total"] == 0
        assert len(data["captures"]) == 0
    
    def test_list_captures_with_sync_status_filter(self, test_db_setup, sample_frame, sample_capture_data):
        """Test listing captures with sync status filter"""
        # Create some captures
        for i in range(2):
            response = client.post("/api/v1/captures/", json=sample_capture_data)
            assert response.status_code == 201
        
        # List with sync status filter
        response = client.get("/api/v1/captures/?sync_status=synced")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 2
        assert all(capture["sync_status"] == "synced" for capture in data["captures"])
    
    def test_list_captures_invalid_sync_status(self, test_db_setup):
        """Test listing captures with invalid sync status"""
        response = client.get("/api/v1/captures/?sync_status=invalid_status")
        
        assert response.status_code == 400
        assert "Invalid sync_status" in response.json()["detail"]
    
    def test_captures_health_check(self, test_db_setup):
        """Test captures health check endpoint"""
        response = client.get("/api/v1/captures/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "captures"
    
    def test_authentication_required(self, test_db_setup):
        """Test that authentication is required for all endpoints"""
        # Override to remove authentication
        app.dependency_overrides[get_current_user] = lambda: None
        
        try:
            # These should fail without authentication
            response = client.get("/api/v1/captures/health")
            # Note: This test depends on your actual authentication implementation
            # You may need to adjust based on how authentication failures are handled
            
        finally:
            # Restore authentication override
            app.dependency_overrides[get_current_user] = override_get_current_user


if __name__ == "__main__":
    pytest.main([__file__])