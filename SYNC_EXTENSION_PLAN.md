# Sync Extension Plan

## Overview
This document outlines the plan to extend sync capabilities for the weld defect detection system, focusing on improved deletion handling, server-to-client sync, and enhanced UI differentiation between local and server-only data.

## Current State Analysis

### Existing Sync Architecture
- **Client-to-Server**: Production-ready with priority-based sync queue
- **Server-to-Client**: Not implemented
- **Deletion**: Basic implementation without confirmation or partial sync consideration
- **UI Differentiation**: Only shows local IndexedDB data

### Current Database Structure

**IndexedDB (Client):**
```typescript
interface FrameRecord {
  frameId: string;
  modelNumber: string;
  machineSerialNumber: string;
  inspectorName: string;
  syncStatus: 'synced' | 'pending' | 'conflict';
  createdAt: Date;
  lastModified: Date;
}

interface CaptureRecord {
  captureId: string;
  frameId: string;
  originalImageBlob: Blob;
  processedImageBlob: Blob;
  detectionResults: DetectionResult[];
  syncStatus: 'synced' | 'pending' | 'conflict';
}
```

**Backend (SQLite):**
- Mirrors IndexedDB structure with additional metadata
- SQLAlchemy relationships with cascading deletes

## Requirements Analysis

### 1. Frame Deletion Issues
- **Problem**: No confirmation for pending sync frames
- **Problem**: No consideration for capture sync status during frame deletion
- **Problem**: No differentiation between local and server-only frames

### 2. Server-to-Client Sync Missing
- **Problem**: No way to fetch server metadata for sessions
- **Problem**: No download capability for server-only frames
- **Problem**: No conflict resolution for concurrent operations

### 3. UI Differentiation Needs
- Display frames from both IndexedDB and server
- Visual indicators for sync status and location
- Download buttons for server-only frames

## Proposed Database Structure Changes

### 1. Enhanced IndexedDB Schema

```typescript
interface FrameRecord {
  frameId: string;
  modelNumber: string;
  machineSerialNumber: string;
  inspectorName: string;
  
  // Enhanced sync tracking
  syncStatus: 'synced' | 'pending' | 'conflict' | 'deleted_locally' | 'server_only';
  localSyncStatus: 'synced' | 'pending' | 'conflict';
  serverSyncStatus: 'synced' | 'pending' | 'not_found';
  
  // Metadata tracking
  isLocallyDeleted: boolean;
  serverExists: boolean;
  localCaptureCount: number;
  serverCaptureCount: number;
  lastServerSync: Date | null;
  
  // Timestamps
  createdAt: Date;
  lastModified: Date;
  serverLastModified: Date | null;
}

interface CaptureRecord {
  captureId: string;
  frameId: string;
  originalImageBlob: Blob;
  processedImageBlob: Blob;
  detectionResults: DetectionResult[];
  
  // Enhanced sync tracking
  syncStatus: 'synced' | 'pending' | 'conflict' | 'deleted_locally' | 'server_only';
  localSyncStatus: 'synced' | 'pending' | 'conflict';
  serverSyncStatus: 'synced' | 'pending' | 'not_found';
  
  // Metadata
  isLocallyDeleted: boolean;
  serverExists: boolean;
  lastServerSync: Date | null;
  
  // Timestamps
  createdAt: Date;
  lastModified: Date;
  serverLastModified: Date | null;
}

// New table for server metadata cache
interface ServerFrameMetadata {
  frameId: string;
  modelNumber: string;
  machineSerialNumber: string;
  inspectorName: string;
  captureCount: number;
  totalSize: number; // in bytes
  createdAt: Date;
  lastModified: Date;
  isDownloaded: boolean;
  lastMetadataSync: Date;
}
```

### 2. Enhanced Backend Schema

```python
# Add to existing models
class Frame(Base):
    # ... existing fields ...
    
    # Soft delete support
    is_deleted = Column(Boolean, default=False)
    deleted_at = Column(DateTime, nullable=True)
    deleted_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Enhanced sync tracking
    version = Column(Integer, default=1)
    last_client_sync = Column(DateTime, nullable=True)
    sync_conflicts = Column(Integer, default=0)

class Capture(Base):
    # ... existing fields ...
    
    # Soft delete support
    is_deleted = Column(Boolean, default=False)
    deleted_at = Column(DateTime, nullable=True)
    
    # Enhanced sync tracking
    version = Column(Integer, default=1)
    last_client_sync = Column(DateTime, nullable=True)

# New table for tracking sync operations
class SyncOperation(Base):
    __tablename__ = "sync_operations"
    
    id = Column(Integer, primary_key=True)
    operation_type = Column(String, nullable=False)  # create, update, delete
    object_type = Column(String, nullable=False)     # frame, capture
    object_id = Column(String, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"))
    client_timestamp = Column(DateTime, nullable=False)
    server_timestamp = Column(DateTime, default=datetime.utcnow)
    status = Column(String, default="completed")     # completed, failed, conflicted
    conflict_resolution = Column(String, nullable=True)
```

## Implementation Plan

### Phase 1: Enhanced Deletion Logic

#### 1.1 Client-Side Deletion Confirmation
```typescript
// New deletion workflow
async function deleteFrameWithConfirmation(frameId: string): Promise<boolean> {
  const frame = await getFrameById(frameId);
  if (!frame) return false;

  // Check frame sync status
  if (frame.syncStatus === 'pending') {
    const confirmed = await showConfirmDialog({
      title: "Unsync Frame Deletion",
      message: "This frame hasn't been synced to the server yet. Deleting it will permanently lose this data. Continue?",
      type: "warning"
    });
    if (!confirmed) return false;
  }

  // Check captures sync status
  const captures = await getCapturesByFrameId(frameId);
  const unsyncedCaptures = captures.filter(c => c.syncStatus === 'pending');
  
  if (unsyncedCaptures.length > 0) {
    const confirmed = await showConfirmDialog({
      title: "Unsync Captures Found",
      message: `This frame has ${unsyncedCaptures.length} captures that haven't been synced. Deleting will lose this data. Continue?`,
      type: "warning"
    });
    if (!confirmed) return false;
  }

  return await performFrameDeletion(frameId);
}

async function performFrameDeletion(frameId: string): Promise<boolean> {
  const frame = await getFrameById(frameId);
  if (!frame) return false;

  if (frame.syncStatus === 'synced' && frame.serverExists) {
    // Soft delete - mark as locally deleted but keep server copy
    await updateFrame(frameId, {
      isLocallyDeleted: true,
      syncStatus: 'deleted_locally',
      lastModified: new Date()
    });
    
    // Add to sync queue for server notification
    await addToSyncQueue({
      operationType: 'soft_delete',
      objectType: 'frame',
      objectId: frameId,
      priority: 8
    });
  } else {
    // Hard delete - remove completely
    await hardDeleteFrame(frameId);
  }

  return true;
}
```

#### 1.2 Server-Side Soft Delete Support
```python
# New endpoint for soft delete
@router.delete("/api/v1/frames/{frame_id}/soft")
async def soft_delete_frame(frame_id: str, current_user: User = Depends(get_current_user)):
    frame = await frame_service.get_frame_by_id(frame_id)
    if not frame:
        raise HTTPException(status_code=404, detail="Frame not found")
    
    # Mark as deleted but keep data
    frame.is_deleted = True
    frame.deleted_at = datetime.utcnow()
    frame.deleted_by = current_user.id
    
    await db.commit()
    return {"status": "soft_deleted", "frame_id": frame_id}
```

### Phase 2: Server-to-Client Sync

#### 2.1 Server Metadata Endpoints
```python
@router.get("/api/v1/frames/metadata")
async def get_frames_metadata(
    inspector_id: Optional[int] = None,
    model_number: Optional[str] = None,
    machine_serial: Optional[str] = None,
    since: Optional[datetime] = None,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """Get frame metadata for sync purposes"""
    frames = await frame_service.get_frames_metadata(
        inspector_id=inspector_id,
        model_number=model_number,
        machine_serial=machine_serial,
        since=since,
        limit=limit,
        offset=offset,
        include_deleted=False
    )
    
    return {
        "frames": [
            {
                "frameId": frame.frame_id,
                "modelNumber": frame.model_number,
                "machineSerialNumber": frame.machine_serial_number,
                "inspectorName": frame.inspector_name,
                "captureCount": frame.capture_count,
                "totalSize": frame.total_size,
                "createdAt": frame.created_at,
                "lastModified": frame.updated_at,
                "version": frame.version
            }
            for frame in frames
        ],
        "totalCount": len(frames),
        "hasMore": len(frames) == limit
    }

@router.get("/api/v1/frames/{frame_id}/download")
async def download_frame_data(
    frame_id: str,
    include_images: bool = True,
    current_user: User = Depends(get_current_user)
):
    """Download complete frame data including captures"""
    frame_data = await frame_service.get_complete_frame_data(
        frame_id=frame_id,
        include_images=include_images
    )
    
    if not frame_data:
        raise HTTPException(status_code=404, detail="Frame not found")
    
    return frame_data
```

#### 2.2 Client-Side Server Sync Manager
```typescript
class ServerSyncManager {
  async fetchServerMetadata(filters?: {
    modelNumber?: string;
    machineSerialNumber?: string;
    since?: Date;
  }): Promise<ServerFrameMetadata[]> {
    const response = await fetch('/api/v1/frames/metadata', {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${getAuthToken()}` },
      body: JSON.stringify(filters)
    });
    
    return response.json();
  }

  async downloadFrameData(frameId: string): Promise<FrameWithCaptures> {
    const response = await fetch(`/api/v1/frames/${frameId}/download`, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${getAuthToken()}` }
    });
    
    const frameData = await response.json();
    
    // Store in IndexedDB
    await this.storeDownloadedFrame(frameData);
    
    return frameData;
  }

  async syncServerMetadata(): Promise<void> {
    const serverMetadata = await this.fetchServerMetadata();
    const localFrames = await getAllFrames();
    
    // Update local metadata cache
    for (const serverFrame of serverMetadata) {
      const localFrame = localFrames.find(f => f.frameId === serverFrame.frameId);
      
      if (!localFrame) {
        // Server-only frame
        await addServerFrameMetadata({
          ...serverFrame,
          isDownloaded: false,
          lastMetadataSync: new Date()
        });
      } else {
        // Update local frame with server info
        await updateFrame(localFrame.frameId, {
          serverExists: true,
          serverCaptureCount: serverFrame.captureCount,
          serverLastModified: serverFrame.lastModified,
          lastServerSync: new Date()
        });
      }
    }
  }
}
```

### Phase 3: Enhanced UI Components

#### 3.1 Enhanced Session Card
```tsx
interface SessionCardProps {
  frame: FrameRecord;
  serverMetadata?: ServerFrameMetadata;
  onDownload?: (frameId: string) => void;
  onDelete?: (frameId: string) => void;
}

export function SessionCard({ frame, serverMetadata, onDownload, onDelete }: SessionCardProps) {
  const isLocalOnly = !frame.serverExists && !serverMetadata;
  const isServerOnly = !frame.frameId && serverMetadata;
  const isSynced = frame.syncStatus === 'synced';
  const hasPendingSync = frame.syncStatus === 'pending';
  const isLocallyDeleted = frame.isLocallyDeleted;

  return (
    <Card className={cn(
      "relative",
      isLocalOnly && "border-blue-200 bg-blue-50",
      isServerOnly && "border-green-200 bg-green-50",
      isLocallyDeleted && "opacity-60 border-gray-200"
    )}>
      {/* Status Indicator */}
      <div className="absolute top-2 right-2">
        {isLocalOnly && <Badge variant="secondary">Local Only</Badge>}
        {isServerOnly && <Badge variant="outline">Server Only</Badge>}
        {hasPendingSync && <Badge variant="destructive">Sync Pending</Badge>}
        {isSynced && <Badge variant="default">Synced</Badge>}
        {isLocallyDeleted && <Badge variant="secondary">Deleted Locally</Badge>}
      </div>

      {/* Frame Content */}
      <CardContent className="pt-8">
        <div className="space-y-2">
          <h3 className="font-semibold">{frame.modelNumber || serverMetadata?.modelNumber}</h3>
          <p className="text-sm text-muted-foreground">
            Serial: {frame.machineSerialNumber || serverMetadata?.machineSerialNumber}
          </p>
          <p className="text-sm text-muted-foreground">
            Inspector: {frame.inspectorName || serverMetadata?.inspectorName}
          </p>
          
          {/* Capture Count */}
          <div className="flex justify-between text-sm">
            <span>Captures:</span>
            <span>
              {frame.localCaptureCount || 0}
              {frame.serverCaptureCount && frame.serverCaptureCount !== frame.localCaptureCount && (
                <span className="text-muted-foreground ml-1">
                  (Server: {frame.serverCaptureCount})
                </span>
              )}
            </span>
          </div>
        </div>
      </CardContent>

      {/* Actions */}
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          {isServerOnly && (
            <Button size="sm" variant="outline" onClick={() => onDownload?.(serverMetadata!.frameId)}>
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          )}
          {!isLocallyDeleted && (
            <Button size="sm" variant="destructive" onClick={() => onDelete?.(frame.frameId)}>
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          )}
        </div>
        
        <div className="text-xs text-muted-foreground">
          {formatDistanceToNow(frame.createdAt || serverMetadata?.createdAt)} ago
        </div>
      </CardFooter>
    </Card>
  );
}
```

#### 3.2 Enhanced Session List
```tsx
export function SessionList() {
  const [localFrames, setLocalFrames] = useState<FrameRecord[]>([]);
  const [serverMetadata, setServerMetadata] = useState<ServerFrameMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [syncManager] = useState(() => new ServerSyncManager());

  useEffect(() => {
    async function loadData() {
      setIsLoading(true);
      
      // Load local frames
      const frames = await getAllFrames();
      setLocalFrames(frames);
      
      // Sync server metadata
      try {
        await syncManager.syncServerMetadata();
        const metadata = await getAllServerMetadata();
        setServerMetadata(metadata);
      } catch (error) {
        console.error('Failed to sync server metadata:', error);
      }
      
      setIsLoading(false);
    }
    
    loadData();
  }, []);

  const handleDownload = async (frameId: string) => {
    try {
      await syncManager.downloadFrameData(frameId);
      // Refresh local frames
      const frames = await getAllFrames();
      setLocalFrames(frames);
    } catch (error) {
      console.error('Failed to download frame:', error);
    }
  };

  const handleDelete = async (frameId: string) => {
    const success = await deleteFrameWithConfirmation(frameId);
    if (success) {
      // Refresh local frames
      const frames = await getAllFrames();
      setLocalFrames(frames);
    }
  };

  // Combine and deduplicate frames
  const allFrames = useMemo(() => {
    const frameMap = new Map<string, { frame?: FrameRecord; metadata?: ServerFrameMetadata }>();
    
    // Add local frames
    localFrames.forEach(frame => {
      frameMap.set(frame.frameId, { frame });
    });
    
    // Add server metadata
    serverMetadata.forEach(metadata => {
      const existing = frameMap.get(metadata.frameId);
      if (existing) {
        existing.metadata = metadata;
      } else {
        frameMap.set(metadata.frameId, { metadata });
      }
    });
    
    return Array.from(frameMap.values());
  }, [localFrames, serverMetadata]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Detection Sessions</h2>
        <Button onClick={() => syncManager.syncServerMetadata()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Sync Server Data
        </Button>
      </div>
      
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {allFrames.map(({ frame, metadata }) => (
            <SessionCard
              key={frame?.frameId || metadata?.frameId}
              frame={frame}
              serverMetadata={metadata}
              onDownload={handleDownload}
              onDelete={handleDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
}
```

## Additional Considerations

### 1. Conflict Resolution Strategy
- **Last Writer Wins**: Simple approach for most cases
- **User Prompt**: For critical conflicts, prompt user to choose
- **Merge Strategy**: For non-conflicting changes, merge automatically

### 2. Performance Optimization
- **Lazy Loading**: Load server metadata on demand
- **Pagination**: Implement pagination for large datasets
- **Caching**: Cache server metadata with expiration
- **Background Sync**: Periodic background metadata sync

### 3. Error Handling
- **Network Failures**: Graceful degradation when offline
- **Authentication**: Handle token expiration and refresh
- **Data Corruption**: Validation and recovery mechanisms
- **Storage Limits**: IndexedDB quota management

### 4. Security Considerations
- **Authorization**: Ensure users can only access their own data
- **Data Validation**: Validate all data before storage
- **Audit Trail**: Log all sync operations for debugging
- **Encryption**: Consider encrypting sensitive data at rest

### 5. Testing Strategy
- **Unit Tests**: Test individual sync operations
- **Integration Tests**: Test complete sync workflows
- **E2E Tests**: Test UI interactions and edge cases
- **Performance Tests**: Test with large datasets

## Migration Plan

### Phase 1: Database Schema Updates (Week 1)
1. Update IndexedDB schema with new fields
2. Create migration scripts for existing data
3. Update backend models with soft delete support
4. Add new server endpoints

### Phase 2: Enhanced Deletion Logic (Week 2)
1. Implement confirmation dialogs
2. Add soft delete logic
3. Update sync queue processing
4. Test deletion workflows

### Phase 3: Server-to-Client Sync (Week 3-4)
1. Implement server metadata endpoints
2. Create ServerSyncManager class
3. Add download functionality
4. Test bidirectional sync

### Phase 4: UI Enhancements (Week 5)
1. Update SessionCard component
2. Enhance SessionList with server data
3. Add visual indicators for sync status
4. Implement download UI

### Phase 5: Testing & Polish (Week 6)
1. Comprehensive testing
2. Performance optimization
3. Error handling improvements
4. Documentation updates

## Success Metrics
- ✅ Deletion confirmation prevents accidental data loss
- ✅ Server metadata sync works reliably
- ✅ UI clearly differentiates local vs server data
- ✅ Download functionality works for server-only frames
- ✅ Soft delete preserves server data while cleaning local storage
- ✅ No sync conflicts or data corruption
- ✅ Performance remains acceptable with large datasets