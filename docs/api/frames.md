# Frames API

The Frames API manages detection sessions (frames) in the weld defect detection system. Each frame represents a detection session with metadata about the inspection process.

## Base URL
```
/api/v1/frames
```

## Authentication
All endpoints require Bearer token authentication.

## Endpoints

### POST /
Create a new frame (detection session).

**Authentication:** Required

**Request:**
```http
POST /api/v1/frames
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "model_number": "WM-2024-A",
  "machine_serial_number": "MSN-12345",
  "inspector_name": "<PERSON>",
  "status": "active",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "morning",
    "notes": "Routine inspection"
  }
}
```

**Request Schema:**
```typescript
interface FrameCreate {
  model_number: string;
  machine_serial_number: string;
  inspector_name: string;
  status?: string;  // Default: "active"
  metadata?: Record<string, any>;
}
```

**Response:**
```json
{
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "model_number": "WM-2024-A",
  "machine_serial_number": "MSN-12345",
  "inspector_name": "John Doe",
  "status": "active",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "morning",
    "notes": "Routine inspection"
  },
  "creation_timestamp": 1703097600000,
  "last_modified_timestamp": 1703097600000,
  "capture_count": 0,
  "sync_status": "synced",
  "last_synced_at": 1703097600000
}
```

**Status Codes:**
- `200 OK`: Frame created successfully
- `401 Unauthorized`: Invalid or missing token
- `400 Bad Request`: Invalid input data

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/frames" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model_number": "WM-2024-A",
    "machine_serial_number": "MSN-12345",
    "inspector_name": "John Doe",
    "status": "active"
  }'
```

---

### GET /
Get list of frames with optional filtering and pagination.

**Authentication:** Required

**Request:**
```http
GET /api/v1/frames?skip=0&limit=20&inspector_name=John%20Doe&status=active
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Number of records to return (default: 100, max: 1000)
- `inspector_name` (string, optional): Filter by inspector name
- `machine_serial_number` (string, optional): Filter by machine serial number
- `status` (string, optional): Filter by status (active, completed, archived)

**Response:**
```json
{
  "frames": [
    {
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "model_number": "WM-2024-A",
      "machine_serial_number": "MSN-12345",
      "inspector_name": "John Doe",
      "status": "active",
      "metadata": {
        "location": "Factory Floor A"
      },
      "creation_timestamp": 1703097600000,
      "last_modified_timestamp": 1703097600000,
      "capture_count": 5,
      "sync_status": "synced",
      "last_synced_at": 1703097600000
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 20
}
```

**Status Codes:**
- `200 OK`: Frames retrieved successfully
- `401 Unauthorized`: Invalid or missing token

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/frames?skip=0&limit=20" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /{frame_id}
Get a specific frame by ID.

**Authentication:** Required

**Request:**
```http
GET /api/v1/frames/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "model_number": "WM-2024-A",
  "machine_serial_number": "MSN-12345",
  "inspector_name": "John Doe",
  "status": "active",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "morning"
  },
  "creation_timestamp": 1703097600000,
  "last_modified_timestamp": 1703097600000,
  "capture_count": 5,
  "sync_status": "synced",
  "last_synced_at": 1703097600000
}
```

**Status Codes:**
- `200 OK`: Frame retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Frame not found

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/frames/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### PUT /{frame_id}
Update an existing frame.

**Authentication:** Required

**Request:**
```http
PUT /api/v1/frames/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "status": "completed",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "morning",
    "completion_notes": "All checks passed"
  }
}
```

**Request Schema:**
```typescript
interface FrameUpdate {
  model_number?: string;
  machine_serial_number?: string;
  inspector_name?: string;
  status?: string;
  metadata?: Record<string, any>;
}
```

**Response:**
```json
{
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "model_number": "WM-2024-A",
  "machine_serial_number": "MSN-12345",
  "inspector_name": "John Doe",
  "status": "completed",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "morning",
    "completion_notes": "All checks passed"
  },
  "creation_timestamp": 1703097600000,
  "last_modified_timestamp": 1703184000000,
  "capture_count": 5,
  "sync_status": "synced",
  "last_synced_at": 1703184000000
}
```

**Status Codes:**
- `200 OK`: Frame updated successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Frame not found

**Example with curl:**
```bash
curl -X PUT "http://localhost:8000/api/v1/frames/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": "completed"}'
```

---

### DELETE /{frame_id}
Delete a frame and all its captures.

**Authentication:** Required

**Request:**
```http
DELETE /api/v1/frames/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "message": "Frame deleted successfully"
}
```

**Status Codes:**
- `200 OK`: Frame deleted successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Frame not found

**Example with curl:**
```bash
curl -X DELETE "http://localhost:8000/api/v1/frames/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /{frame_id}/details
Get frame with all its captures.

**Authentication:** Required

**Request:**
```http
GET /api/v1/frames/550e8400-e29b-41d4-a716-446655440000/details
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "frame": {
    "frame_id": "550e8400-e29b-41d4-a716-446655440000",
    "model_number": "WM-2024-A",
    "machine_serial_number": "MSN-12345",
    "inspector_name": "John Doe",
    "status": "active",
    "creation_timestamp": 1703097600000,
    "last_modified_timestamp": 1703097600000,
    "capture_count": 2,
    "sync_status": "synced"
  },
  "captures": [
    {
      "capture_id": "650e8400-e29b-41d4-a716-446655440001",
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "capture_timestamp": 1703097660000,
      "detection_results": [
        {
          "id": "det_001",
          "class": "crack",
          "confidence": 0.85,
          "bbox": {
            "x1": 100,
            "y1": 150,
            "x2": 200,
            "y2": 250
          }
        }
      ],
      "sync_status": "synced"
    }
  ]
}
```

**Status Codes:**
- `200 OK`: Frame with captures retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Frame not found

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/frames/550e8400-e29b-41d4-a716-446655440000/details" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /search/
Search frames by model number, machine serial, or inspector name.

**Authentication:** Required

**Request:**
```http
GET /api/v1/frames/search/?q=WM-2024&skip=0&limit=20
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `q` (string, required): Search query (minimum 1 character)
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Number of records to return (default: 100, max: 1000)

**Response:**
```json
{
  "frames": [
    {
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "model_number": "WM-2024-A",
      "machine_serial_number": "MSN-12345",
      "inspector_name": "John Doe",
      "status": "active",
      "creation_timestamp": 1703097600000,
      "last_modified_timestamp": 1703097600000,
      "capture_count": 5,
      "sync_status": "synced"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 20
}
```

**Status Codes:**
- `200 OK`: Search completed successfully
- `401 Unauthorized`: Invalid or missing token
- `400 Bad Request`: Invalid search query

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/frames/search/?q=WM-2024" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Data Models

### Frame Response Schema
```typescript
interface FrameResponse {
  frame_id: string;
  model_number: string;
  machine_serial_number: string;
  inspector_name: string;
  status: string;
  metadata?: Record<string, any>;
  creation_timestamp: number;
  last_modified_timestamp: number;
  capture_count: number;
  sync_status: "synced" | "pending" | "conflict";
  last_synced_at?: number;
}
```

### Frame List Response Schema
```typescript
interface FrameListResponse {
  frames: FrameResponse[];
  total: number;
  page: number;
  limit: number;
}
```

## Frame Status Values

- `active`: Frame is currently being used for detection
- `completed`: Frame session has been completed
- `archived`: Frame has been archived for long-term storage

## Sync Status Values

- `synced`: Frame is synchronized with the server
- `pending`: Frame has local changes pending sync
- `conflict`: Frame has sync conflicts that need resolution

## Error Responses

```json
{
  "detail": "Error description"
}
```

Common error codes:
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Invalid or missing token
- `404`: Not Found - Frame not found
- `500`: Internal Server Error - Server-side error