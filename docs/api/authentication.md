# Authentication API

The Authentication API handles user registration, login, JWT token management, and password operations.

## Base URL
```
/api/v1/auth
```

## Endpoints

### POST /login
Authenticate user and return access and refresh tokens.

**Request:**
```http
POST /api/v1/auth/login
Content-Type: application/x-www-form-urlencoded

username=inspector1&password=password123
```

**Request Body (Form Data):**
- `username` (string, required): User's username
- `password` (string, required): User's password

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response Schema:**
```typescript
interface Token {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
}
```

**Status Codes:**
- `200 OK`: Authentication successful
- `401 Unauthorized`: Invalid credentials

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=inspector1&password=password123"
```

---

### POST /register
Register a new user account.

**Request:**
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "full_name": "New User",
  "password": "securepassword123",
  "role": "inspector"
}
```

**Request Schema:**
```typescript
interface UserRegister {
  username: string;    // 3-50 characters
  email: string;       // Valid email format
  full_name: string;   // 1-100 characters
  password: string;    // Minimum 6 characters
  role: "admin" | "inspector";  // Default: "inspector"
}
```

**Response:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "newuser",
  "full_name": "New User",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": *************,
  "last_login": null
}
```

**Status Codes:**
- `200 OK`: Registration successful
- `400 Bad Request`: Username or email already exists, validation errors
- `500 Internal Server Error`: Failed to create user

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "full_name": "New User",
    "password": "securepassword123",
    "role": "inspector"
  }'
```

---

### GET /me
Get current authenticated user's profile information.

**Authentication:** Required (Bearer token)

**Request:**
```http
GET /api/v1/auth/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "inspector1",
  "full_name": "Inspector One",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": *************,
  "last_login": 1703184000000
}
```

**Status Codes:**
- `200 OK`: User profile retrieved
- `401 Unauthorized`: Invalid or missing token

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### POST /refresh
Refresh access token using refresh token.

**Request:**
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Request Schema:**
```typescript
interface RefreshTokenRequest {
  refresh_token: string;
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Status Codes:**
- `200 OK`: Token refresh successful
- `401 Unauthorized`: Invalid refresh token or user not found

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "YOUR_REFRESH_TOKEN"}'
```

---

### POST /forgot-password
Request password reset email.

**Request:**
```http
POST /api/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Request Schema:**
```typescript
interface PasswordResetRequest {
  email: string;  // Valid email format
}
```

**Response:**
```json
{
  "message": "If the email exists, a password reset link has been sent"
}
```

**Status Codes:**
- `200 OK`: Always returns success (prevents email enumeration)

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

---

### POST /reset-password
Reset password using reset token from email.

**Request:**
```http
POST /api/v1/auth/reset-password
Content-Type: application/json

{
  "token": "reset_token_from_email",
  "new_password": "newpassword123"
}
```

**Request Schema:**
```typescript
interface PasswordReset {
  token: string;
  new_password: string;  // Minimum 6 characters
}
```

**Response:**
```json
{
  "message": "Password has been reset successfully"
}
```

**Status Codes:**
- `200 OK`: Password reset successful
- `400 Bad Request`: Invalid or expired reset token
- `500 Internal Server Error`: Failed to update password

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "reset_token_from_email",
    "new_password": "newpassword123"
  }'
```

---

### POST /change-password
Change current user's password.

**Authentication:** Required (Bearer token)

**Request:**
```http
POST /api/v1/auth/change-password
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "current_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

**Request Schema:**
```typescript
interface PasswordChange {
  current_password: string;
  new_password: string;  // Minimum 6 characters
}
```

**Response:**
```json
{
  "message": "Password changed successfully"
}
```

**Status Codes:**
- `200 OK`: Password change successful
- `400 Bad Request`: Current password is incorrect
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Failed to update password

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/auth/change-password" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "current_password": "oldpassword123",
    "new_password": "newpassword123"
  }'
```

## JWT Token Configuration

- **Access Token Expiry:** 30 minutes
- **Refresh Token Expiry:** 7 days
- **Reset Token Expiry:** 60 minutes
- **Algorithm:** HS256

## User Roles

- **admin**: Full system access, can manage users
- **inspector**: Can create and manage detection sessions

## Error Responses

All authentication endpoints may return the following error format:

```json
{
  "detail": "Error description"
}
```

Common error codes:
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Invalid credentials or token
- `403`: Forbidden - Insufficient permissions
- `500`: Internal Server Error - Server-side error

## Security Notes

- All passwords are hashed using secure algorithms
- JWT tokens use different secrets for access and refresh tokens
- Password reset tokens include random nonce for security
- Email enumeration is prevented in forgot password endpoint
- Rate limiting should be implemented for authentication endpoints