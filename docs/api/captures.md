# Captures API

The Captures API manages individual detection captures within frames. Each capture contains image data and AI detection results from the weld defect detection system.

## Base URL
```
/api/v1/captures
```

## Authentication
All endpoints require Bearer token authentication.

## Endpoints

### POST /
Create a new capture.

**Authentication:** Required

**Request:**
```http
POST /api/v1/captures
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "detection_results": [
    {
      "id": "det_001",
      "class": "crack",
      "confidence": 0.85,
      "bbox": {
        "x1": 100.5,
        "y1": 150.2,
        "x2": 200.8,
        "y2": 250.1
      }
    },
    {
      "id": "det_002", 
      "class": "porosity",
      "confidence": 0.72,
      "bbox": {
        "x1": 300.1,
        "y1": 400.5,
        "x2": 350.9,
        "y2": 450.3
      }
    }
  ],
  "original_image_data": null,
  "processed_image_data": null
}
```

**Request Schema:**
```typescript
interface CaptureCreate {
  frame_id: string;
  detection_results: DetectionResult[];
  original_image_data?: bytes;    // Optional, max 10MB
  processed_image_data?: bytes;   // Optional, max 10MB
}

interface DetectionResult {
  id: string;
  class: string;        // Detected object class name
  confidence: number;   // Confidence score 0.0-1.0
  bbox: {
    x1: number;         // Bounding box coordinates
    y1: number;
    x2: number;
    y2: number;
  };
}
```

**Response:**
```json
{
  "capture_id": "650e8400-e29b-41d4-a716-446655440001",
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "capture_timestamp": 1703097660000,
  "detection_results": [
    {
      "id": "det_001",
      "class": "crack",
      "confidence": 0.85,
      "bbox": {
        "x1": 100.5,
        "y1": 150.2,
        "x2": 200.8,
        "y2": 250.1
      }
    }
  ],
  "sync_status": "synced",
  "sync_version": 1,
  "last_sync_attempt": 1703097660000,
  "has_original_image": false,
  "has_processed_image": false,
  "has_thumbnail": false,
  "image_sizes": null
}
```

**Status Codes:**
- `201 Created`: Capture created successfully
- `400 Bad Request`: Invalid input data, frame not found, or validation errors
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Failed to create capture

**Validation Rules:**
- Maximum 100 detection results per capture
- Maximum 10MB per image blob
- Frame must exist

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/captures" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "frame_id": "550e8400-e29b-41d4-a716-446655440000",
    "detection_results": [
      {
        "id": "det_001",
        "class": "crack",
        "confidence": 0.85,
        "bbox": {"x1": 100, "y1": 150, "x2": 200, "y2": 250}
      }
    ]
  }'
```

---

### GET /{capture_id}
Get a specific capture by ID.

**Authentication:** Required

**Request:**
```http
GET /api/v1/captures/650e8400-e29b-41d4-a716-446655440001
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "capture_id": "650e8400-e29b-41d4-a716-446655440001",
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "capture_timestamp": 1703097660000,
  "detection_results": [
    {
      "id": "det_001",
      "class": "crack",
      "confidence": 0.85,
      "bbox": {
        "x1": 100.5,
        "y1": 150.2,
        "x2": 200.8,
        "y2": 250.1
      }
    }
  ],
  "sync_status": "synced",
  "sync_version": 1,
  "last_sync_attempt": 1703097660000,
  "has_original_image": true,
  "has_processed_image": true,
  "has_thumbnail": true,
  "image_sizes": {
    "original": 1024000,
    "processed": 512000,
    "thumbnail": 32000
  }
}
```

**Status Codes:**
- `200 OK`: Capture retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Capture not found
- `500 Internal Server Error`: Failed to retrieve capture

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/captures/650e8400-e29b-41d4-a716-446655440001" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### PUT /{capture_id}
Update an existing capture.

**Authentication:** Required

**Request:**
```http
PUT /api/v1/captures/650e8400-e29b-41d4-a716-446655440001?sync_version=1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "detection_results": [
    {
      "id": "det_001",
      "class": "crack",
      "confidence": 0.92,
      "bbox": {
        "x1": 100.5,
        "y1": 150.2,
        "x2": 200.8,
        "y2": 250.1
      }
    }
  ]
}
```

**Query Parameters:**
- `sync_version` (integer, optional): Expected sync version for optimistic locking

**Request Schema:**
```typescript
interface CaptureUpdate {
  detection_results?: DetectionResult[];
  processed_image_data?: bytes;  // Optional, max 10MB
}
```

**Response:**
```json
{
  "capture_id": "650e8400-e29b-41d4-a716-446655440001",
  "frame_id": "550e8400-e29b-41d4-a716-446655440000",
  "capture_timestamp": 1703097660000,
  "detection_results": [
    {
      "id": "det_001",
      "class": "crack",
      "confidence": 0.92,
      "bbox": {
        "x1": 100.5,
        "y1": 150.2,
        "x2": 200.8,
        "y2": 250.1
      }
    }
  ],
  "sync_status": "synced",
  "sync_version": 2,
  "last_sync_attempt": 1703097720000,
  "has_original_image": true,
  "has_processed_image": true,
  "has_thumbnail": true
}
```

**Status Codes:**
- `200 OK`: Capture updated successfully
- `400 Bad Request`: Invalid input data or validation errors
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Capture not found
- `409 Conflict`: Version conflict (optimistic locking failure)
- `500 Internal Server Error`: Failed to update capture

**Example with curl:**
```bash
curl -X PUT "http://localhost:8000/api/v1/captures/650e8400-e29b-41d4-a716-446655440001" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "detection_results": [
      {
        "id": "det_001",
        "class": "crack", 
        "confidence": 0.92,
        "bbox": {"x1": 100, "y1": 150, "x2": 200, "y2": 250}
      }
    ]
  }'
```

---

### DELETE /{capture_id}
Delete a capture.

**Authentication:** Required

**Request:**
```http
DELETE /api/v1/captures/650e8400-e29b-41d4-a716-446655440001
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
- `204 No Content` (empty body)

**Status Codes:**
- `204 No Content`: Capture deleted successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Capture not found
- `500 Internal Server Error`: Failed to delete capture

**Example with curl:**
```bash
curl -X DELETE "http://localhost:8000/api/v1/captures/650e8400-e29b-41d4-a716-446655440001" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /frames/{frame_id}/captures
Get all captures for a specific frame with pagination.

**Authentication:** Required

**Request:**
```http
GET /api/v1/captures/frames/550e8400-e29b-41d4-a716-446655440000/captures?page=1&limit=20&sort_by=capture_timestamp&sort_order=desc
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `page` (integer, optional): Page number, 1-based (default: 1)
- `limit` (integer, optional): Number of captures per page (default: 20, max: 100)
- `sort_by` (string, optional): Field to sort by (default: "capture_timestamp")
- `sort_order` (string, optional): Sort order "asc" or "desc" (default: "desc")

**Response:**
```json
{
  "captures": [
    {
      "capture_id": "650e8400-e29b-41d4-a716-446655440001",
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "capture_timestamp": 1703097660000,
      "detection_results": [
        {
          "id": "det_001",
          "class": "crack",
          "confidence": 0.85,
          "bbox": {
            "x1": 100.5,
            "y1": 150.2,
            "x2": 200.8,
            "y2": 250.1
          }
        }
      ],
      "sync_status": "synced",
      "sync_version": 1,
      "has_original_image": true,
      "has_processed_image": true,
      "has_thumbnail": true
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 20,
  "has_next": true,
  "has_previous": false
}
```

**Status Codes:**
- `200 OK`: Captures retrieved successfully
- `400 Bad Request`: Invalid query parameters
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Failed to retrieve captures

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/captures/frames/550e8400-e29b-41d4-a716-446655440000/captures?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /
List captures with optional filtering.

**Authentication:** Required

**Request:**
```http
GET /api/v1/captures?frame_id=550e8400-e29b-41d4-a716-446655440000&sync_status=pending&page=1&limit=20
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `frame_id` (string, optional): Filter captures by frame ID
- `sync_status` (string, optional): Filter by sync status ("synced", "pending", "conflict")
- `page` (integer, optional): Page number, 1-based (default: 1)
- `limit` (integer, optional): Number of captures per page (default: 20, max: 100)
- `sort_by` (string, optional): Field to sort by (default: "capture_timestamp")
- `sort_order` (string, optional): Sort order "asc" or "desc" (default: "desc")

**Response:**
```json
{
  "captures": [
    {
      "capture_id": "650e8400-e29b-41d4-a716-446655440001",
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "capture_timestamp": 1703097660000,
      "detection_results": [],
      "sync_status": "pending",
      "sync_version": 1,
      "has_original_image": false,
      "has_processed_image": false,
      "has_thumbnail": false
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 20,
  "has_next": false,
  "has_previous": false
}
```

**Status Codes:**
- `200 OK`: Captures retrieved successfully
- `400 Bad Request`: Invalid sync_status value
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Failed to list captures

**Note:** If no filters are provided, returns empty list to prevent returning all captures.

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/captures?frame_id=550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /{capture_id}/storage
Get storage information for a capture.

**Authentication:** Required

**Request:**
```http
GET /api/v1/captures/650e8400-e29b-41d4-a716-446655440001/storage
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "capture_id": "650e8400-e29b-41d4-a716-446655440001",
  "storage_paths": {
    "original_image": "/storage/captures/650e8400.../original.jpg",
    "processed_image": "/storage/captures/650e8400.../processed.jpg",
    "thumbnail": "/storage/captures/650e8400.../thumbnail.jpg"
  },
  "file_sizes": {
    "original_image": 1024000,
    "processed_image": 512000,
    "thumbnail": 32000
  },
  "total_size": 1568000,
  "compression_ratio": 0.75
}
```

**Status Codes:**
- `200 OK`: Storage info retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Capture not found
- `500 Internal Server Error`: Failed to get storage info

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/captures/650e8400-e29b-41d4-a716-446655440001/storage" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### POST /{capture_id}/thumbnails
Regenerate thumbnails for a capture.

**Authentication:** Required

**Request:**
```http
POST /api/v1/captures/650e8400-e29b-41d4-a716-446655440001/thumbnails
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "message": "Thumbnails regenerated successfully"
}
```

**Status Codes:**
- `200 OK`: Thumbnails regenerated successfully
- `401 Unauthorized`: Invalid or missing token
- `404 Not Found`: Capture not found or has no original image
- `500 Internal Server Error`: Failed to regenerate thumbnails

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/captures/650e8400-e29b-41d4-a716-446655440001/thumbnails" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /storage/metrics
Get storage service metrics.

**Authentication:** Required

**Request:**
```http
GET /api/v1/captures/storage/metrics
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "total_captures": 1250,
  "total_storage_used": **********,
  "storage_by_type": {
    "original_images": **********,
    "processed_images": 536870912,
    "thumbnails": 536870912
  },
  "average_capture_size": 1717987,
  "compression_efficiency": 0.72,
  "storage_health": "healthy"
}
```

**Status Codes:**
- `200 OK`: Metrics retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Failed to get storage metrics

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/captures/storage/metrics" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### GET /health
Health check endpoint for captures service.

**Request:**
```http
GET /api/v1/captures/health
```

**Response:**
```json
{
  "status": "healthy",
  "service": "captures",
  "storage": {
    "status": "healthy",
    "available_space": **********,
    "used_space": **********
  }
}
```

**Status Codes:**
- `200 OK`: Service is healthy

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/captures/health"
```

## Data Models

### Capture Response Schema
```typescript
interface CaptureResponse {
  capture_id: string;
  frame_id: string;
  capture_timestamp: number;
  detection_results: DetectionResult[];
  sync_status: "synced" | "pending" | "conflict";
  sync_version: number;
  last_sync_attempt?: number;
  has_original_image: boolean;
  has_processed_image: boolean;
  has_thumbnail: boolean;
  image_sizes?: {
    original?: number;
    processed?: number;
    thumbnail?: number;
  };
}
```

### Capture List Response Schema
```typescript
interface CaptureListResponse {
  captures: CaptureResponse[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}
```

### Detection Result Schema
```typescript
interface DetectionResult {
  id: string;
  class: string;        // e.g., "crack", "porosity", "slag"
  confidence: number;   // 0.0 to 1.0
  bbox: {
    x1: number;         // Top-left x coordinate
    y1: number;         // Top-left y coordinate
    x2: number;         // Bottom-right x coordinate
    y2: number;         // Bottom-right y coordinate
  };
}
```

## File Handling

- **Image Upload:** Images are stored as binary blobs in the database
- **Size Limits:** Maximum 10MB per image
- **Supported Formats:** JPEG, PNG, and other common image formats
- **Storage:** Original, processed, and thumbnail versions are maintained
- **Compression:** Automatic compression for processed images and thumbnails

## Sync Status Values

- `synced`: Capture is synchronized with the server
- `pending`: Capture has local changes pending sync
- `conflict`: Capture has sync conflicts that need resolution

## Error Responses

```json
{
  "detail": "Error description"
}
```

Common error codes:
- `400`: Bad Request - Invalid input data or validation errors
- `401`: Unauthorized - Invalid or missing token
- `404`: Not Found - Capture not found
- `409`: Conflict - Version conflict during update
- `500`: Internal Server Error - Server-side error