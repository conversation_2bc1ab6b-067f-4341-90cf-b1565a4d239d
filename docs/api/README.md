# Weld Defect Detection API Documentation

This documentation covers the complete REST API for the Weld Defect Detection System, an offline-first web application built with FastAPI that provides real-time weld defect detection using YOLOv8n AI models.

## Overview

The API is designed with an **offline-first architecture** that enables seamless operation when disconnected from the server. All operations are stored locally first and synchronized with the server when connectivity is available.

### Key Features
- **JWT-based Authentication** with role-based access control
- **Offline-first Synchronization** with conflict resolution
- **Image Processing** with AI-powered defect detection
- **Real-time Detection Results** storage and management
- **Comprehensive User Management** with admin controls

## Base URL

```
http://localhost:8000
```

All API endpoints are prefixed with `/api/v1/`.

## API Modules

### [Authentication API](./authentication.md)
Handles user registration, login, JWT token management, and password operations.

**Base Path:** `/api/v1/auth`

**Key Endpoints:**
- `POST /login` - User authentication
- `POST /register` - User registration
- `GET /me` - Current user profile
- `POST /refresh` - Token refresh
- `POST /forgot-password` - Password reset request
- `POST /reset-password` - Password reset
- `POST /change-password` - Change password

### [Frames API](./frames.md)
Manages detection sessions (frames) with metadata about inspection processes.

**Base Path:** `/api/v1/frames`

**Key Endpoints:**
- `POST /` - Create new frame
- `GET /` - List frames with filtering
- `GET /{frame_id}` - Get specific frame
- `PUT /{frame_id}` - Update frame
- `DELETE /{frame_id}` - Delete frame
- `GET /{frame_id}/details` - Get frame with captures
- `GET /search/` - Search frames

### [Captures API](./captures.md)
Manages individual detection captures with image data and AI results.

**Base Path:** `/api/v1/captures`

**Key Endpoints:**
- `POST /` - Create new capture
- `GET /{capture_id}` - Get specific capture
- `PUT /{capture_id}` - Update capture
- `DELETE /{capture_id}` - Delete capture
- `GET /frames/{frame_id}/captures` - Get frame captures
- `GET /` - List captures with filtering
- `GET /{capture_id}/storage` - Storage information
- `POST /{capture_id}/thumbnails` - Regenerate thumbnails

### [Sync API](./sync.md)
Handles synchronization between client and server for offline-first architecture.

**Base Path:** `/api/v1/sync`

**Key Endpoints:**
- `POST /frame` - Sync single frame
- `POST /capture` - Sync single capture with images
- `POST /batch` - Batch sync operations
- `GET /health` - Sync service health
- `GET /stats` - User sync statistics

### [Users API](./users.md)
Manages user accounts and administrative functions.

**Base Path:** `/api/v1/users`

**Key Endpoints:**
- `GET /` - List users (admin only)
- `GET /me` - Current user profile
- `PUT /me` - Update own profile
- `GET /{user_id}` - Get user by ID (admin only)
- `PUT /{user_id}` - Update user (admin only)
- `DELETE /{user_id}` - Delete user (admin only)
- `POST /{user_id}/deactivate` - Deactivate user (admin only)

## Authentication

### JWT Bearer Token
All protected endpoints require a Bearer token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token Lifecycle
- **Access Token:** 30 minutes expiry
- **Refresh Token:** 7 days expiry
- **Reset Token:** 60 minutes expiry

### User Roles
- **Admin:** Full system access, user management
- **Inspector:** Detection operations, own profile management

## Data Models

### Core Entities

#### Frame (Detection Session)
```typescript
interface Frame {
  frame_id: string;
  model_number: string;
  machine_serial_number: string;
  inspector_name: string;
  status: "active" | "completed" | "archived";
  creation_timestamp: number;
  last_modified_timestamp: number;
  capture_count: number;
  sync_status: "synced" | "pending" | "conflict";
  metadata?: Record<string, any>;
}
```

#### Capture (Individual Detection)
```typescript
interface Capture {
  capture_id: string;
  frame_id: string;
  capture_timestamp: number;
  detection_results: DetectionResult[];
  sync_status: "synced" | "pending" | "conflict";
  sync_version: number;
  has_original_image: boolean;
  has_processed_image: boolean;
  has_thumbnail: boolean;
}
```

#### Detection Result
```typescript
interface DetectionResult {
  id: string;
  class: string;        // e.g., "crack", "porosity", "slag"
  confidence: number;   // 0.0 to 1.0
  bbox: {
    x1: number;         // Bounding box coordinates
    y1: number;
    x2: number;
    y2: number;
  };
}
```

#### User
```typescript
interface User {
  user_id: string;
  username: string;
  full_name: string;
  email: string;
  role: "admin" | "inspector";
  is_active: boolean;
  created_at: number;
  last_login?: number;
}
```

## Sync Architecture

### Offline-First Design
1. **Local Storage:** All operations stored in IndexedDB first
2. **Sync Queue:** Background queue tracks pending operations
3. **Conflict Resolution:** Server detects and reports conflicts
4. **Eventual Consistency:** System reaches consistent state after sync

### Sync Operations
- **Frame Sync:** Metadata and session information
- **Capture Sync:** Detection results with image upload support
- **Batch Sync:** Multiple operations in single request (max 50 items)
- **Conflict Detection:** Timestamp-based conflict detection

### Sync Status Values
- `synced`: Synchronized with server
- `pending`: Local changes awaiting sync
- `conflict`: Conflicts requiring resolution

## Image Handling

### Storage
- **Original Images:** Raw camera captures
- **Processed Images:** AI-annotated results
- **Thumbnails:** Compressed previews
- **Blob Storage:** Binary data in database
- **Size Limits:** 10MB per image

### Formats
- Supported: JPEG, PNG, and common image formats
- Automatic compression for processed images and thumbnails
- Storage optimization with compression ratios tracked

## Error Handling

### Standard Error Response
```json
{
  "detail": "Error description"
}
```

### Common HTTP Status Codes
- `200 OK`: Successful operation
- `201 Created`: Resource created successfully
- `204 No Content`: Successful deletion
- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Sync conflict or constraint violation
- `500 Internal Server Error`: Server-side error

## Rate Limiting & Performance

### Pagination
Most list endpoints support pagination:
- `page`: Page number (1-based)
- `limit`/`page_size`: Items per page
- `skip`: Records to skip (0-based)

### Filtering & Search
- Query parameters for filtering by common fields
- Full-text search on relevant endpoints
- Sorting support for list endpoints

### Performance Features
- Database indexes for optimized queries
- Batch operations for efficient sync
- Image compression and thumbnails
- Background processing for heavy operations

## Development & Testing

### Environment Setup
```bash
# Backend
cd backend
uv sync
uv run uvicorn app.main:app --reload

# Frontend
cd frontend
npm run dev
```

### API Testing
```bash
# Health check
curl http://localhost:8000/api/v1/captures/health

# Authentication test
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=inspector1&password=password123"
```

### Default Test Users
- **Admin:** username: `admin`, password: `admin123`
- **Inspector:** username: `inspector1`, password: `password123`

## API Versioning

- Current version: `v1`
- Version included in URL: `/api/v1/`
- Backward compatibility maintained within major versions

## Security Considerations

1. **JWT Security:** Separate secrets for access and refresh tokens
2. **Password Security:** Secure hashing with salt
3. **Role-Based Access:** Endpoint-level permission checking
4. **Input Validation:** Comprehensive validation on all inputs
5. **File Security:** Size limits and format validation for uploads
6. **Audit Trail:** User actions and timestamps tracked

## Client Integration Examples

### Authentication Flow
```javascript
// Login
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  body: 'username=inspector1&password=password123'
});
const { access_token, refresh_token } = await loginResponse.json();

// Use token
const framesResponse = await fetch('/api/v1/frames', {
  headers: { 'Authorization': `Bearer ${access_token}` }
});
```

### Creating Detection Session
```javascript
// Create frame
const frameData = {
  model_number: "WM-2024-A",
  machine_serial_number: "MSN-12345",
  inspector_name: "John Doe",
  status: "active"
};

const frame = await fetch('/api/v1/frames', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(frameData)
});

// Add capture with detection results
const captureData = {
  frame_id: frame.frame_id,
  detection_results: [
    {
      id: "det_001",
      class: "crack",
      confidence: 0.85,
      bbox: { x1: 100, y1: 150, x2: 200, y2: 250 }
    }
  ]
};

await fetch('/api/v1/captures', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(captureData)
});
```

### Batch Sync Operation
```javascript
const syncData = {
  requests: [
    {
      operation_type: "create",
      object_type: "frame",
      object_id: "frame-123",
      frame_data: frameData
    },
    {
      operation_type: "create",
      object_type: "capture",
      object_id: "capture-456",
      frame_id: "frame-123",
      capture_data: captureData
    }
  ]
};

const syncResult = await fetch('/api/v1/sync/batch', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(syncData)
});
```

## Support & Documentation

For detailed endpoint documentation, see individual API module files:
- [Authentication API Documentation](./authentication.md)
- [Frames API Documentation](./frames.md)
- [Captures API Documentation](./captures.md)
- [Sync API Documentation](./sync.md)
- [Users API Documentation](./users.md)

Each document provides complete request/response examples, error codes, and usage patterns for the respective API modules.