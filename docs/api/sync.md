# Sync API

The Sync API handles synchronization of frames and captures between client and server, providing conflict resolution and batch processing capabilities for offline-first architecture.

## Base URL
```
/api/v1/sync
```

## Authentication
All endpoints require Bearer token authentication.

## Endpoints

### POST /frame
Sync a single frame from client to server.

**Authentication:** Required

**Request:**
```http
POST /api/v1/sync/frame
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "operation_type": "create",
  "object_type": "frame",
  "object_id": "550e8400-e29b-41d4-a716-446655440000",
  "frame_data": {
    "frame_id": "550e8400-e29b-41d4-a716-446655440000",
    "model_number": "WM-2024-A",
    "machine_serial_number": "MSN-12345",
    "inspector_name": "<PERSON> Doe",
    "status": "active",
    "creation_timestamp": 1703097600000,
    "last_modified_timestamp": 1703097600000,
    "metadata": {
      "location": "Factory Floor A",
      "shift": "morning"
    }
  }
}
```

**Request Schema:**
```typescript
interface SyncFrameRequest {
  operation_type: "create" | "update" | "delete";
  object_type: "frame";
  object_id: string;
  client_id?: string;
  frame_data: {
    frame_id: string;
    model_number: string;
    machine_serial_number: string;
    inspector_name: string;
    status: string;
    creation_timestamp: number;
    last_modified_timestamp: number;
    metadata?: Record<string, any>;
  };
}
```

**Response:**
```json
{
  "success": true,
  "message": "Frame synced successfully",
  "object_id": "550e8400-e29b-41d4-a716-446655440000",
  "object_type": "frame",
  "server_object_id": "550e8400-e29b-41d4-a716-446655440000",
  "conflicts": null
}
```

**Status Codes:**
- `200 OK`: Frame sync successful
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Frame sync failed

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/sync/frame" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "operation_type": "create",
    "object_type": "frame",
    "object_id": "550e8400-e29b-41d4-a716-446655440000",
    "frame_data": {
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "model_number": "WM-2024-A",
      "machine_serial_number": "MSN-12345",
      "inspector_name": "John Doe",
      "status": "active",
      "creation_timestamp": 1703097600000,
      "last_modified_timestamp": 1703097600000
    }
  }'
```

---

### POST /capture
Sync a single capture from client to server with image upload support.

**Authentication:** Required

**Request:**
```http
POST /api/v1/sync/capture
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data

operation_type=create
object_type=capture
object_id=650e8400-e29b-41d4-a716-446655440001
frame_id=550e8400-e29b-41d4-a716-446655440000
capture_data={"capture_id":"650e8400-e29b-41d4-a716-446655440001","frame_id":"550e8400-e29b-41d4-a716-446655440000","capture_timestamp":1703097660000,"detection_results":[{"id":"det_001","class":"crack","confidence":0.85,"bbox":{"x1":100,"y1":150,"x2":200,"y2":250}}]}
original_image=@/path/to/original.jpg
processed_image=@/path/to/processed.jpg
thumbnail_image=@/path/to/thumbnail.jpg
```

**Form Parameters:**
- `operation_type` (string, required): "create", "update", or "delete"
- `object_type` (string, required): "capture"
- `object_id` (string, required): Capture ID
- `frame_id` (string, required): Parent frame ID
- `capture_data` (string, required): JSON string with capture data
- `original_image` (file, optional): Original image file
- `processed_image` (file, optional): Processed image file
- `thumbnail_image` (file, optional): Thumbnail image file

**Capture Data JSON Structure:**
```typescript
interface CaptureData {
  capture_id: string;
  frame_id: string;
  capture_timestamp: number;
  detection_results: DetectionResult[];
  // Image blobs are added from form files
}
```

**Response:**
```json
{
  "success": true,
  "message": "Capture synced successfully",
  "object_id": "650e8400-e29b-41d4-a716-446655440001",
  "object_type": "capture",
  "server_object_id": "650e8400-e29b-41d4-a716-446655440001",
  "conflicts": null
}
```

**Status Codes:**
- `200 OK`: Capture sync successful
- `400 Bad Request`: Invalid capture_data JSON format
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Capture sync failed

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/sync/capture" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "operation_type=create" \
  -F "object_type=capture" \
  -F "object_id=650e8400-e29b-41d4-a716-446655440001" \
  -F "frame_id=550e8400-e29b-41d4-a716-446655440000" \
  -F 'capture_data={"capture_id":"650e8400-e29b-41d4-a716-446655440001","frame_id":"550e8400-e29b-41d4-a716-446655440000","capture_timestamp":1703097660000,"detection_results":[]}' \
  -F "original_image=@original.jpg" \
  -F "processed_image=@processed.jpg"
```

---

### POST /batch
Sync multiple items in a single batch request.

**Authentication:** Required

**Request:**
```http
POST /api/v1/sync/batch
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "requests": [
    {
      "operation_type": "create",
      "object_type": "frame",
      "object_id": "550e8400-e29b-41d4-a716-446655440000",
      "frame_data": {
        "frame_id": "550e8400-e29b-41d4-a716-446655440000",
        "model_number": "WM-2024-A",
        "machine_serial_number": "MSN-12345",
        "inspector_name": "John Doe",
        "status": "active",
        "creation_timestamp": 1703097600000,
        "last_modified_timestamp": 1703097600000
      }
    },
    {
      "operation_type": "create",
      "object_type": "capture",
      "object_id": "650e8400-e29b-41d4-a716-446655440001",
      "frame_id": "550e8400-e29b-41d4-a716-446655440000",
      "capture_data": {
        "capture_id": "650e8400-e29b-41d4-a716-446655440001",
        "frame_id": "550e8400-e29b-41d4-a716-446655440000",
        "capture_timestamp": 1703097660000,
        "detection_results": [
          {
            "id": "det_001",
            "class": "crack",
            "confidence": 0.85,
            "bbox": {"x1": 100, "y1": 150, "x2": 200, "y2": 250}
          }
        ]
      }
    }
  ],
  "client_id": "user_12345"
}
```

**Request Schema:**
```typescript
interface SyncBatchRequest {
  requests: (SyncFrameRequest | SyncCaptureRequest)[];  // Max 50 items
  client_id?: string;
}
```

**Response:**
```json
{
  "results": [
    {
      "success": true,
      "message": "Frame synced successfully",
      "object_id": "550e8400-e29b-41d4-a716-446655440000",
      "object_type": "frame"
    },
    {
      "success": true,
      "message": "Capture synced successfully", 
      "object_id": "650e8400-e29b-41d4-a716-446655440001",
      "object_type": "capture"
    }
  ],
  "total_requested": 2,
  "successful": 2,
  "failed": 0,
  "errors": null
}
```

**Response Schema:**
```typescript
interface SyncBatchResponse {
  results: SyncResponse[];
  total_requested: number;
  successful: number;
  failed: number;
  errors?: SyncErrorDetail[];
}
```

**Status Codes:**
- `200 OK`: Batch processing completed (check individual results for success/failure)
- `401 Unauthorized`: Invalid or missing token

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/sync/batch" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "operation_type": "create",
        "object_type": "frame",
        "object_id": "550e8400-e29b-41d4-a716-446655440000",
        "frame_data": {
          "frame_id": "550e8400-e29b-41d4-a716-446655440000",
          "model_number": "WM-2024-A",
          "machine_serial_number": "MSN-12345",
          "inspector_name": "John Doe",
          "status": "active",
          "creation_timestamp": 1703097600000,
          "last_modified_timestamp": 1703097600000
        }
      }
    ]
  }'
```

---

### GET /health
Health check for sync service.

**Request:**
```http
GET /api/v1/sync/health
```

**Response:**
```json
{
  "status": "healthy",
  "service": "sync",
  "timestamp": 1703097600000
}
```

**Status Codes:**
- `200 OK`: Service is healthy

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/sync/health"
```

---

### GET /stats
Get sync statistics for the current user.

**Authentication:** Required

**Request:**
```http
GET /api/v1/sync/stats
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "user_id": "user_12345",
  "frames_synced": 45,
  "captures_synced": 234,
  "last_sync": 1703097600000,
  "pending_sync_items": 3
}
```

**Status Codes:**
- `200 OK`: Statistics retrieved successfully
- `401 Unauthorized`: Invalid or missing token

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/sync/stats" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Data Models

### Sync Response Schema
```typescript
interface SyncResponse {
  success: boolean;
  message: string;
  object_id: string;
  object_type: "frame" | "capture";
  server_object_id?: string;  // If server assigns different ID
  conflicts?: string[];       // List of conflicting fields
}
```

### Sync Error Detail Schema
```typescript
interface SyncErrorDetail {
  error_code: string;
  error_message: string;
  field?: string;
}
```

### Sync Frame Request Schema
```typescript
interface SyncFrameRequest {
  operation_type: "create" | "update" | "delete";
  object_type: "frame";
  object_id: string;
  client_id?: string;
  frame_data: FrameData;
}
```

### Sync Capture Request Schema
```typescript
interface SyncCaptureRequest {
  operation_type: "create" | "update" | "delete";
  object_type: "capture";
  object_id: string;
  frame_id: string;
  client_id?: string;
  capture_data: CaptureData;
}
```

## Operation Types

- `create`: Create new object on server
- `update`: Update existing object on server
- `delete`: Delete object from server

## Conflict Resolution

When conflicts occur during sync operations, the response includes a `conflicts` array listing the conflicting fields:

```json
{
  "success": false,
  "message": "Conflict detected",
  "object_id": "550e8400-e29b-41d4-a716-446655440000",
  "object_type": "frame",
  "conflicts": ["last_modified_timestamp", "status"]
}
```

Clients should handle conflicts by:
1. Presenting conflict resolution UI to users
2. Merging changes automatically where possible
3. Re-syncing with resolved data

## Batch Processing

- Maximum 50 requests per batch
- Each request in batch is processed individually
- Partial success is possible (some items succeed, others fail)
- Results array corresponds to request array order
- Use batch processing for efficient offline sync

## Best Practices

### Client Implementation
1. **Queue Management**: Implement local sync queue for offline operations
2. **Retry Logic**: Implement exponential backoff for failed sync attempts
3. **Conflict Handling**: Provide user-friendly conflict resolution
4. **Progress Tracking**: Show sync progress to users
5. **Error Handling**: Gracefully handle network failures

### Performance Optimization
1. **Batch Operations**: Use batch sync for multiple items
2. **Image Compression**: Compress images before sync
3. **Delta Sync**: Only sync changed data when possible
4. **Background Sync**: Perform sync operations in background

### Error Handling
```javascript
// Example error handling
try {
  const response = await syncFrame(frameData);
  if (!response.success) {
    if (response.conflicts) {
      // Handle conflicts
      await resolveConflicts(response.conflicts);
    } else {
      // Handle other errors
      console.error('Sync failed:', response.message);
    }
  }
} catch (error) {
  // Handle network errors
  await retrySync(frameData);
}
```

## Error Responses

```json
{
  "detail": "Error description"
}
```

Common error codes:
- `400`: Bad Request - Invalid JSON format or request structure
- `401`: Unauthorized - Invalid or missing token
- `500`: Internal Server Error - Server-side sync failure

## Sync Architecture

The sync system follows an **offline-first** architecture:

1. **Client Operations**: All operations stored locally first
2. **Sync Queue**: Background queue tracks pending sync operations
3. **Conflict Detection**: Server detects conflicts using timestamps/versions
4. **Resolution**: Conflicts resolved through user interaction or automatic rules
5. **Eventual Consistency**: System reaches consistent state after sync completion

This design ensures the application works seamlessly offline and syncs when connectivity is restored.