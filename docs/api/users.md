# Users API

The Users API handles user management operations including listing users, updating profiles, and administrative functions. This API provides role-based access control with admin and user-level operations.

## Base URL
```
/api/v1/users
```

## Authentication
All endpoints require Bearer token authentication.

## Endpoints

### GET /
List all users (admin only).

**Authentication:** Required (Admin role)

**Request:**
```http
GET /api/v1/users?page=1&page_size=20&role=inspector&active_only=true
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `page` (integer, optional): Page number (default: 1)
- `page_size` (integer, optional): Items per page (default: 20, max: 100)
- `role` (string, optional): Filter by role ("admin" or "inspector")
- `active_only` (boolean, optional): Show only active users (default: true)

**Response:**
```json
{
  "users": [
    {
      "user_id": "550e8400-e29b-41d4-a716-************",
      "username": "inspector1",
      "full_name": "Inspector One",
      "email": "<EMAIL>",
      "role": "inspector",
      "is_active": true,
      "created_at": *************,
      "last_login": *************
    },
    {
      "user_id": "660f9511-f39c-52e5-b827-557766551111",
      "username": "admin",
      "full_name": "System Admin",
      "email": "<EMAIL>",
      "role": "admin",
      "is_active": true,
      "created_at": 1703010000000,
      "last_login": 1703183000000
    }
  ],
  "total": 2,
  "page": 1,
  "page_size": 20,
  "has_next": false
}
```

**Response Schema:**
```typescript
interface UserListResponse {
  users: UserResponse[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
}
```

**Status Codes:**
- `200 OK`: Users retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `403 Forbidden`: Admin access required

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/users?page=1&page_size=20" \
  -H "Authorization: Bearer YOUR_ADMIN_ACCESS_TOKEN"
```

---

### GET /me
Get current user's profile information.

**Authentication:** Required

**Request:**
```http
GET /api/v1/users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "inspector1",
  "full_name": "Inspector One",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": *************,
  "last_login": *************
}
```

**Status Codes:**
- `200 OK`: User profile retrieved successfully
- `401 Unauthorized`: Invalid or missing token

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

### PUT /me
Update current user's profile.

**Authentication:** Required

**Request:**
```http
PUT /api/v1/users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "full_name": "Inspector One Updated",
  "email": "<EMAIL>"
}
```

**Request Schema:**
```typescript
interface UserUpdate {
  username?: string;        // 3-50 characters
  email?: string;          // Valid email format
  full_name?: string;      // 1-100 characters
  role?: "admin" | "inspector";     // Admin only
  is_active?: boolean;     // Admin only
}
```

**Note:** Regular users cannot change their own `role` or `is_active` status. These fields are ignored for non-admin users.

**Response:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "inspector1",
  "full_name": "Inspector One Updated",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": *************,
  "last_login": *************
}
```

**Status Codes:**
- `200 OK`: Profile updated successfully
- `400 Bad Request`: Username or email already taken, validation errors
- `401 Unauthorized`: Invalid or missing token
- `500 Internal Server Error`: Failed to update user

**Example with curl:**
```bash
curl -X PUT "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "Inspector One Updated",
    "email": "<EMAIL>"
  }'
```

---

### GET /{user_id}
Get user by ID (admin only).

**Authentication:** Required (Admin role)

**Request:**
```http
GET /api/v1/users/550e8400-e29b-41d4-a716-************
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "inspector1",
  "full_name": "Inspector One",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": *************,
  "last_login": *************
}
```

**Status Codes:**
- `200 OK`: User retrieved successfully
- `401 Unauthorized`: Invalid or missing token
- `403 Forbidden`: Admin access required
- `404 Not Found`: User not found

**Example with curl:**
```bash
curl -X GET "http://localhost:8000/api/v1/users/550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer YOUR_ADMIN_ACCESS_TOKEN"
```

---

### PUT /{user_id}
Update user by ID (admin only).

**Authentication:** Required (Admin role)

**Request:**
```http
PUT /api/v1/users/550e8400-e29b-41d4-a716-************
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "full_name": "Inspector One - Updated by Admin",
  "role": "admin",
  "is_active": true
}
```

**Request Schema:**
```typescript
interface UserUpdate {
  username?: string;        // 3-50 characters
  email?: string;          // Valid email format
  full_name?: string;      // 1-100 characters
  role?: "admin" | "inspector";
  is_active?: boolean;
}
```

**Response:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "inspector1",
  "full_name": "Inspector One - Updated by Admin",
  "email": "<EMAIL>",
  "role": "admin",
  "is_active": true,
  "created_at": *************,
  "last_login": *************
}
```

**Special Rules:**
- Admins cannot deactivate their own account
- Username and email must be unique across all users

**Status Codes:**
- `200 OK`: User updated successfully
- `400 Bad Request`: Cannot deactivate own account, username/email already taken, validation errors
- `401 Unauthorized`: Invalid or missing token
- `403 Forbidden`: Admin access required
- `404 Not Found`: User not found
- `500 Internal Server Error`: Failed to update user

**Example with curl:**
```bash
curl -X PUT "http://localhost:8000/api/v1/users/550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer YOUR_ADMIN_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "admin",
    "is_active": true
  }'
```

---

### DELETE /{user_id}
Delete user by ID (admin only).

**Authentication:** Required (Admin role)

**Request:**
```http
DELETE /api/v1/users/550e8400-e29b-41d4-a716-************
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "message": "User deleted successfully"
}
```

**Special Rules:**
- Admins cannot delete their own account

**Status Codes:**
- `200 OK`: User deleted successfully
- `400 Bad Request`: Cannot delete own account
- `401 Unauthorized`: Invalid or missing token
- `403 Forbidden`: Admin access required
- `404 Not Found`: User not found
- `500 Internal Server Error`: Failed to delete user

**Example with curl:**
```bash
curl -X DELETE "http://localhost:8000/api/v1/users/550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer YOUR_ADMIN_ACCESS_TOKEN"
```

---

### POST /{user_id}/deactivate
Deactivate user (admin only).

**Authentication:** Required (Admin role)

**Request:**
```http
POST /api/v1/users/550e8400-e29b-41d4-a716-************/deactivate
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "message": "User deactivated successfully"
}
```

**Special Rules:**
- Admins cannot deactivate their own account
- Deactivated users cannot log in but their data is preserved

**Status Codes:**
- `200 OK`: User deactivated successfully
- `400 Bad Request`: Cannot deactivate own account
- `401 Unauthorized`: Invalid or missing token
- `403 Forbidden`: Admin access required
- `404 Not Found`: User not found
- `500 Internal Server Error`: Failed to deactivate user

**Example with curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/users/550e8400-e29b-41d4-a716-************/deactivate" \
  -H "Authorization: Bearer YOUR_ADMIN_ACCESS_TOKEN"
```

## Data Models

### User Response Schema
```typescript
interface UserResponse {
  user_id: string;
  username: string;
  full_name: string;
  email: string;
  role: "admin" | "inspector";
  is_active: boolean;
  created_at: number;        // Unix timestamp in milliseconds
  last_login?: number;       // Unix timestamp in milliseconds
}
```

### User Update Schema
```typescript
interface UserUpdate {
  username?: string;         // 3-50 characters, must be unique
  email?: string;           // Valid email format, must be unique
  full_name?: string;       // 1-100 characters
  role?: "admin" | "inspector";      // Admin can change, users cannot
  is_active?: boolean;      // Admin can change, users cannot
}
```

### User List Response Schema
```typescript
interface UserListResponse {
  users: UserResponse[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
}
```

## User Roles

### Admin Role
- **Full System Access**: Can manage all users and system settings
- **User Management**: Create, read, update, delete, and deactivate users
- **Role Assignment**: Can promote/demote users between roles
- **Self-Protection**: Cannot delete or deactivate their own account

### Inspector Role
- **Limited Access**: Can manage their own profile and create detection sessions
- **Profile Management**: Can update their own name and email
- **No Admin Functions**: Cannot access user management endpoints
- **Role Restrictions**: Cannot change their own role or active status

## Permission Matrix

| Endpoint | Admin | Inspector | Notes |
|----------|-------|-----------|-------|
| GET /users | ✅ | ❌ | List users |
| GET /users/me | ✅ | ✅ | Own profile |
| PUT /users/me | ✅ | ✅* | *Limited fields for inspector |
| GET /users/{id} | ✅ | ❌ | View any user |
| PUT /users/{id} | ✅ | ❌ | Update any user |
| DELETE /users/{id} | ✅ | ❌ | Delete user |
| POST /users/{id}/deactivate | ✅ | ❌ | Deactivate user |

## Validation Rules

### Username
- 3-50 characters
- Must be unique across all users
- Cannot be changed to existing username

### Email
- Valid email format required
- Must be unique across all users
- Cannot be changed to existing email

### Full Name
- 1-100 characters
- Required field

### Role
- Must be "admin" or "inspector"
- Only admins can change roles
- Default role for new users is "inspector"

## Security Considerations

1. **Role-Based Access**: Endpoints enforce role requirements
2. **Self-Protection**: Admins cannot delete/deactivate themselves
3. **Data Integrity**: Unique constraints on username and email
4. **Audit Trail**: User actions are logged (timestamps tracked)
5. **Deactivation vs Deletion**: Deactivation preserves data while preventing access

## Error Responses

```json
{
  "detail": "Error description"
}
```

Common error codes:
- `400`: Bad Request - Validation errors, constraint violations
- `401`: Unauthorized - Invalid or missing token
- `403`: Forbidden - Insufficient permissions (non-admin accessing admin endpoints)
- `404`: Not Found - User not found
- `500`: Internal Server Error - Server-side error

## Common Use Cases

### Admin Operations
```javascript
// List all active inspectors
const inspectors = await fetch('/api/v1/users?role=inspector&active_only=true');

// Deactivate problematic user
await fetch('/api/v1/users/{user_id}/deactivate', { method: 'POST' });

// Promote inspector to admin
await fetch('/api/v1/users/{user_id}', {
  method: 'PUT',
  body: JSON.stringify({ role: 'admin' })
});
```

### User Profile Management
```javascript
// Get own profile
const profile = await fetch('/api/v1/users/me');

// Update profile
await fetch('/api/v1/users/me', {
  method: 'PUT',
  body: JSON.stringify({
    full_name: 'New Name',
    email: '<EMAIL>'
  })
});
```