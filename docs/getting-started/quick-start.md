# Quick Start Guide

Get the Weld Defect Detection system running in 5 minutes or less.

## Prerequisites

- **Python 3.12+** (currently using 3.12.3)
- **Node.js 18+** (currently using 22.16.0)
- **npm 8+** (currently using 11.4.2)
- **uv** (modern Python package manager)

## Installation

### 1. Install uv (Python Package Manager)

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Restart your terminal or run:
source ~/.bashrc  # Linux
source ~/.zshrc   # macOS with zsh
```

### 2. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd weld_fast

# Backend setup (30 seconds)
cd backend
uv sync
cd ..

# Frontend setup (2-3 minutes)
cd frontend
npm install
cd ..
```

## Quick Start

### Terminal 1 - Backend Server
```bash
cd backend
uv run uvicorn app.main:app --reload
```
Server starts at: http://localhost:8000

### Terminal 2 - Frontend Development
```bash
cd frontend
npm run dev
```
Application opens at: http://localhost:3000

### Terminal 3 - Quick Test (Optional)
```bash
# Test backend connectivity
cd backend
uv run python test_api.py

# Test sync functionality
uv run python test_sync.py
```

## Default Login Credentials

- **Admin**: `admin` / `admin123`
- **Inspector**: `inspector1` / `password123`

## Quick Verification

1. Open http://localhost:3000
2. Login with inspector credentials
3. Create a new detection session:
   - Model Number: `WELD-001`
   - Machine Serial: `TEST-123`
   - Inspector Name: `Test Inspector`
4. Access camera and capture test images
5. Verify detections appear in history panel

## Next Steps

- Read [Installation Guide](installation.md) for detailed setup
- Review [Development Setup](../development/setup.md) for advanced configuration
- Check [Testing Guide](../development/testing.md) for testing procedures

## Common Issues

### Backend won't start
```bash
# Check Python version
python3 --version  # Should be 3.12+

# Reinstall dependencies
cd backend
uv sync --reinstall
```

### Frontend build errors
```bash
# Clear cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### Camera access denied
- Ensure you're using HTTPS or localhost
- Check browser permissions for camera access
- Try a different browser (Chrome recommended)

## Architecture Overview

```
┌─────────────────┐    HTTP/WebSocket    ┌──────────────────┐
│  Frontend       │◄──────────────────► │  Backend         │
│  (Next.js)      │                     │  (FastAPI)       │
│  Port 3000      │                     │  Port 8000       │
└─────────────────┘                     └──────────────────┘
         │                                        │
         ▼                                        ▼
┌─────────────────┐                     ┌──────────────────┐
│  IndexedDB      │                     │  SQLite          │
│  (Client-side)  │                     │  (Server-side)   │
│  Offline Storage│                     │  Persistent DB   │
└─────────────────┘                     └──────────────────┘
```

The system is **offline-first** - all data is stored locally in IndexedDB and synchronized to the backend when connected.