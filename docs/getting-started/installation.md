# Installation Guide

Comprehensive installation instructions for the Weld Defect Detection system.

## System Requirements

### Minimum Requirements
- **OS**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Camera**: USB or built-in camera for detection functionality
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Software Dependencies
- **Python**: 3.12 or higher (tested with 3.12.3)
- **Node.js**: 18.0 or higher (tested with 22.16.0)
- **npm**: 8.0 or higher (tested with 11.4.2)
- **uv**: Latest version (Python package manager)

## Pre-Installation Setup

### 1. Install Python 3.12+

#### macOS
```bash
# Using Homebrew
brew install python@3.12

# Using pyenv
pyenv install 3.12.3
pyenv global 3.12.3
```

#### Windows
```bash
# Download from python.org or use winget
winget install Python.Python.3.12

# Or using Chocolatey
choco install python312
```

#### Linux (Ubuntu/Debian)
```bash
# Add deadsnakes PPA for latest Python versions
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update
sudo apt install python3.12 python3.12-venv python3.12-dev
```

### 2. Install Node.js and npm

#### Using Node Version Manager (Recommended)
```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart terminal or run:
source ~/.bashrc

# Install and use Node.js
nvm install 22.16.0
nvm use 22.16.0
nvm alias default 22.16.0
```

#### Direct Installation
- Download from [nodejs.org](https://nodejs.org/)
- Choose LTS version (18.x or higher)

### 3. Install uv (Modern Python Package Manager)

#### macOS/Linux
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### Windows
```bash
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

#### Alternative (using pip)
```bash
pip install uv
```

**Restart your terminal** after installing uv.

## Project Installation

### 1. Clone Repository
```bash
git clone <your-repository-url>
cd weld_fast
```

### 2. Backend Setup

```bash
cd backend

# Install dependencies using uv (recommended)
uv sync

# Alternative: Create virtual environment manually
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### Verify Backend Installation
```bash
# Check installed packages
uv run pip list

# Test basic import
uv run python -c "import fastapi; print('FastAPI installed successfully')"
```

### 3. Frontend Setup

```bash
cd ../frontend

# Install dependencies
npm install

# Alternative: Use npm ci for production-like install
npm ci
```

#### Verify Frontend Installation
```bash
# Check installed packages
npm list --depth=0

# Verify TypeScript compilation
npx tsc --noEmit
```

## Database Setup

The system uses SQLite for the backend database and IndexedDB for frontend offline storage.

### Backend Database
```bash
cd backend

# Database tables are created automatically on first run
# Default users are also created automatically

# To manually initialize (if needed):
uv run python -c "
from app.database import create_tables
from app.auth import create_default_users
from app.database import get_db
import asyncio

async def init_db():
    await create_tables()
    async for db in get_db():
        await create_default_users(db)
        break

asyncio.run(init_db())
"
```

### Frontend Database
IndexedDB is set up automatically in the browser. No manual configuration needed.

## Configuration

### Backend Configuration
Create a `.env` file in the `backend` directory (optional):

```bash
cd backend
cat > .env << 'EOF'
# Database
DATABASE_URL=sqlite:///./weld_detection.db

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
EOF
```

### Frontend Configuration
Next.js configuration is in `next.config.ts`. Default settings work for development.

For custom API endpoints, create `frontend/.env.local`:
```bash
cd frontend
cat > .env.local << 'EOF'
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
EOF
```

## Verification

### 1. Start Services

#### Terminal 1 - Backend
```bash
cd backend
uv run uvicorn app.main:app --reload
```
✅ Should show: "Uvicorn running on http://127.0.0.1:8000"

#### Terminal 2 - Frontend
```bash
cd frontend
npm run dev
```
✅ Should show: "Next.js ready on http://localhost:3000"

### 2. Test API Connectivity
```bash
cd backend

# Test basic API endpoints
uv run python test_api.py

# Test sync functionality
uv run python test_sync.py
```

### 3. Test Frontend
1. Open http://localhost:3000
2. Login with `inspector1` / `password123`
3. Create a detection session
4. Test camera access
5. Capture and process images

## TensorFlow.js Model Setup

The YOLOv8n model files are already included in the repository:

```
frontend/public/models/yolov8n_web_model/
├── model.json
├── metadata.yaml
├── group1-shard1of4.bin
├── group1-shard2of4.bin
├── group1-shard3of4.bin
└── group1-shard4of4.bin
```

No additional setup required for AI/ML functionality.

## Troubleshooting

### Python Issues
```bash
# Check Python version
python3 --version

# Check if uv is properly installed
uv --version

# Reinstall dependencies
cd backend
uv sync --reinstall
```

### Node.js Issues
```bash
# Check Node version
node --version
npm --version

# Clear npm cache
npm cache clean --force

# Remove and reinstall dependencies
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### Port Conflicts
```bash
# Check what's using port 8000 (backend)
lsof -i :8000  # macOS/Linux
netstat -ano | findstr :8000  # Windows

# Check what's using port 3000 (frontend)
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# Use different ports if needed
cd backend
uv run uvicorn app.main:app --reload --port 8001

cd frontend
npm run dev -- --port 3001
```

### Database Issues
```bash
# Remove and recreate database
cd backend
rm -f weld_detection.db
# Restart backend server to recreate
```

### Camera Access Issues
- Ensure you're accessing via `http://localhost:3000` (not `127.0.0.1`)
- Check browser permissions for camera access
- Try different browsers (Chrome is most compatible)
- For HTTPS deployment, ensure SSL certificates are valid

## Next Steps

After successful installation:

1. Review [Development Setup](../development/setup.md) for advanced configuration
2. Read [Testing Guide](../development/testing.md) for testing procedures
3. Check [API Documentation](../api/) for backend API reference
4. Explore [Architecture Documentation](../architecture/) for system design

## Production Deployment

For production deployment, see:
- Backend: Configure proper database, environment variables, and reverse proxy
- Frontend: Run `npm run build` and serve static files
- Security: Change default passwords, configure HTTPS, set proper CORS origins