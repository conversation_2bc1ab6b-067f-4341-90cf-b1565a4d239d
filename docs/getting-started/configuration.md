# Configuration Guide

This guide covers environment configuration, deployment settings, and customization options for the Weld Defect Detection System.

## 🔧 Environment Configuration

### **Backend Configuration**

The backend uses environment variables and configuration files for settings.

#### Environment Variables

Create a `.env` file in the `backend/` directory:

```bash
# Security
SECRET_KEY=your-super-secret-key-here-change-in-production
ALGORITHM=HS256

# Database
DATABASE_URL=sqlite+aiosqlite:///./weld_detection.db

# JWT Token Settings
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_EXTENSIONS=.jpg,.jpeg,.png

# CORS Settings (for production)
CORS_ORIGINS=http://localhost:3000,https://your-frontend-domain.com

# Optional: Email settings (for password reset)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

#### Production Security Settings

```bash
# Generate a secure secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Example production .env
SECRET_KEY=AbCdEf123456789_secure_random_key_here
DATABASE_URL=sqlite+aiosqlite:///./production_weld_detection.db
ACCESS_TOKEN_EXPIRE_MINUTES=15
CORS_ORIGINS=https://your-production-domain.com
```

### **Frontend Configuration**

The frontend uses Next.js configuration for build and runtime settings.

#### Environment Variables

Create a `.env.local` file in the `frontend/` directory:

```bash
# API Base URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# For production
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com

# Optional: Analytics or monitoring
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
```

#### Next.js Configuration

The `next.config.ts` file contains build and runtime configuration:

```typescript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features
  experimental: {
    // Turbopack for faster development
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  
  // Image optimization
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
```

## 🗄️ Database Configuration

### **Development Database**

The default SQLite configuration works out of the box:

```python
# backend/app/config.py
DATABASE_URL = "sqlite+aiosqlite:///./weld_detection.db"
```

### **Production Database**

For production, consider these database options:

#### SQLite (Recommended for small-medium deployments)
```bash
# Use absolute path for production
DATABASE_URL=sqlite+aiosqlite:////var/lib/weld-detection/weld_detection.db
```

#### PostgreSQL (For high-performance deployments)
```bash
# Install dependencies
pip install asyncpg psycopg2-binary

# Environment configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost/weld_detection
```

#### MySQL (Alternative option)
```bash
# Install dependencies
pip install aiomysql

# Environment configuration
DATABASE_URL=mysql+aiomysql://user:password@localhost/weld_detection
```

### **Database Migrations**

Initialize and run migrations:

```bash
cd backend

# Initialize Alembic (if not already done)
alembic init alembic

# Create migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

## 🔐 Authentication Configuration

### **JWT Settings**

Configure JWT token behavior:

```python
# backend/app/config.py
class Settings(BaseSettings):
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # Password policy
    min_password_length: int = 8
    require_special_chars: bool = True
```

### **Role-Based Access Control**

Default roles and permissions:

```python
# Admin role permissions
ADMIN_PERMISSIONS = [
    "user.create",
    "user.read",
    "user.update", 
    "user.delete",
    "frame.all",
    "capture.all",
    "sync.admin"
]

# Inspector role permissions  
INSPECTOR_PERMISSIONS = [
    "frame.create",
    "frame.read_own",
    "frame.update_own",
    "capture.create",
    "capture.read_own",
    "sync.own"
]
```

## 🎯 AI Model Configuration

### **YOLOv8 Model Settings**

Configure AI model behavior:

```typescript
// frontend/src/lib/detection/config.ts
export const MODEL_CONFIG = {
  // Model file location
  modelPath: '/models/yolov8n_web_model/model.json',
  
  // Inference settings
  inputSize: 640,
  confidenceThreshold: 0.5,
  iouThreshold: 0.4,
  maxDetections: 100,
  
  // Performance settings
  useWebGL: true,
  useWebWorker: false,
  
  // Device-specific optimization
  mobileOptimization: true,
  lowEndDeviceThreshold: 2, // CPU cores
};
```

### **Model Performance Tuning**

Adjust based on device capabilities:

```typescript
// Automatic device optimization
export function getOptimalModelConfig() {
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const cores = navigator.hardwareConcurrency || 2;
  const memory = (navigator as any).deviceMemory || 4;
  
  if (isMobile || cores <= 2 || memory <= 4) {
    return {
      inputSize: 320,
      confidenceThreshold: 0.6,
      maxDetections: 50,
    };
  }
  
  return MODEL_CONFIG;
}
```

## 📱 Camera Configuration

### **WebRTC Settings**

Configure camera capture behavior:

```typescript
// Camera constraints
export const CAMERA_CONFIG = {
  video: {
    width: { ideal: 1920, min: 640 },
    height: { ideal: 1080, min: 480 },
    frameRate: { ideal: 30, min: 15 },
    facingMode: 'environment', // Back camera on mobile
  },
  audio: false,
};

// Capture settings
export const CAPTURE_CONFIG = {
  format: 'image/jpeg',
  quality: 0.9,
  targetSize: 640, // Square crop size
  thumbnailSize: 150,
};
```

## 🔄 Sync Configuration

### **Synchronization Settings**

Configure offline-first sync behavior:

```typescript
// frontend/src/lib/sync/config.ts
export const SYNC_CONFIG = {
  // Timing
  syncInterval: 30000, // 30 seconds
  retryDelayBase: 1000, // 1 second
  maxRetryDelay: 60000, // 1 minute
  maxRetries: 5,
  
  // Batch settings
  batchSize: 10,
  maxConcurrentUploads: 3,
  
  // Storage limits
  maxOfflineStorage: 100 * 1024 * 1024, // 100MB
  maxCapturesPerFrame: 1000,
  
  // Network
  networkTimeoutMs: 30000,
  heartbeatInterval: 120000, // 2 minutes
};
```

## 🚀 Deployment Configuration

### **Production Deployment**

#### Docker Configuration

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite+aiosqlite:///./data/weld_detection.db
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://backend:8000
    depends_on:
      - backend
    restart: unless-stopped
```

#### Nginx Configuration

Create `nginx.conf` for reverse proxy:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Static files
    location /models/ {
        root /var/www/weld-detection;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### **Performance Configuration**

#### Backend Performance

```python
# backend/app/main.py
app = FastAPI(
    title="Weld Detection API",
    description="Production API for weld defect detection",
    version="1.0.0",
    docs_url="/docs" if DEBUG else None,  # Disable in production
    redoc_url="/redoc" if DEBUG else None,
)

# Add performance middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database connection pooling
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
}
```

#### Frontend Performance

```typescript
// next.config.ts
const nextConfig = {
  // Production optimizations
  swcMinify: true,
  compress: true,
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 31536000, // 1 year
  },
  
  // Bundle analysis
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Bundle analyzer in production builds
      config.optimization.splitChunks.chunks = 'all';
    }
    return config;
  },
};
```

## 🔍 Monitoring Configuration

### **Health Checks**

Configure health monitoring:

```python
# backend/app/routers/health.py
@router.get("/health/detailed")
async def detailed_health():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0",
        "components": {
            "database": await check_database_health(),
            "storage": await check_storage_health(),
            "auth": await check_auth_health(),
        }
    }
```

### **Logging Configuration**

```python
# backend/app/config.py
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler",
        },
        "file": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": "logs/app.log",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": False
        }
    }
}
```

## 🔧 Customization Options

### **UI Theming**

Customize the interface appearance:

```css
/* frontend/src/app/globals.css */
:root {
  /* Brand colors */
  --primary: 220 87% 56%;
  --primary-foreground: 0 0% 98%;
  
  /* Detection colors */
  --detection-box: 120 100% 50%;
  --detection-label: 0 0% 0%;
  
  /* Status colors */
  --success: 120 100% 30%;
  --warning: 45 100% 50%;
  --error: 0 100% 50%;
}
```

### **Detection Classes**

Customize detected object classes:

```typescript
// frontend/src/lib/detection/classes.ts
export const CUSTOM_CLASSES = {
  // Weld-specific classes
  'good_weld': { color: '#00ff00', priority: 1 },
  'defective_weld': { color: '#ff0000', priority: 2 },
  'incomplete_weld': { color: '#ffff00', priority: 3 },
  
  // Standard COCO classes
  'person': { color: '#ff9999', priority: 10 },
  // ... other classes
};
```

---

This configuration guide provides comprehensive settings for all aspects of the system. For specific deployment scenarios or custom requirements, refer to the relevant sections and adapt the configurations as needed.