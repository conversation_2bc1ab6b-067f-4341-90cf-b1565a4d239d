# Weld Defect Detection System - Sync API Documentation

## Overview

The Sync API provides endpoints for synchronizing data between the offline-first client (IndexedDB) and the server database. This API handles the bidirectional synchronization of frames (detection sessions) and captures (individual detections) with support for create, update, and delete operations.

## Base URL

```
http://localhost:8000/api/v1/sync
```

## Authentication

All sync endpoints require JWT Bearer authentication. Include the token in the Authorization header:

```
Authorization: Bearer <access_token>
```

### Authentication Flow

1. Login via `/api/v1/auth/login` to get access token
2. Include token in all sync requests
3. Tokens expire after 30 minutes (configurable)

**Example Authentication Error Response:**
```json
{
  "detail": "Could not validate credentials"
}
```

---

## Endpoints

### 1. Individual Frame Sync

Synchronize a single frame (detection session) from client to server.

**Endpoint:** `POST /api/v1/sync/frame`

**Request Schema:**
```json
{
  "operation_type": "create" | "update" | "delete",
  "object_type": "frame",
  "object_id": "string",
  "client_id": "string (optional)",
  "frame_data": {
    "frameId": "string",
    "modelNumber": "string", 
    "machineSerialNumber": "string",
    "inspectorName": "string",
    "inspectorId": "string (optional)",
    "creationTimestamp": "number",
    "lastModifiedTimestamp": "number", 
    "status": "active" | "completed" | "archived",
    "captureCount": "number",
    "metadata": "object (optional)"
  }
}
```

**Response Schema:**
```json
{
  "success": "boolean",
  "message": "string",
  "object_id": "string",
  "object_type": "frame",
  "server_object_id": "string (optional)",
  "conflicts": ["string"] (optional)
}
```

**Success Response Examples:**

*Create Success:*
```json
{
  "success": true,
  "message": "Frame created successfully",
  "object_id": "frame-123",
  "object_type": "frame",
  "server_object_id": "frame-123"
}
```

*Update Success:*
```json
{
  "success": true,
  "message": "Frame updated successfully", 
  "object_id": "frame-123",
  "object_type": "frame",
  "server_object_id": "frame-123"
}
```

*Delete Success:*
```json
{
  "success": true,
  "message": "Frame deleted successfully",
  "object_id": "frame-123",
  "object_type": "frame"
}
```

**Error Response Examples:**

*Server Error (500):*
```json
{
  "detail": "Frame sync failed: Database connection error"
}
```

**HTTP Status Codes:**
- `200` - Success
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (inactive user)
- `500` - Internal Server Error

**Duplicate Handling:**
- CREATE operation on existing frame returns success with "already exists" message
- UPDATE operation on non-existent frame creates the frame instead
- DELETE operation on non-existent frame returns success

---

### 2. Individual Capture Sync

Synchronize a single capture (detection result) with image upload support.

**Endpoint:** `POST /api/v1/sync/capture`

**Content-Type:** `multipart/form-data`

**Form Parameters:**
- `operation_type` (required): "create" | "update" | "delete"
- `object_type` (required): "capture" 
- `object_id` (required): string
- `frame_id` (required): string
- `capture_data` (required): JSON string with capture data
- `original_image` (optional): File upload
- `processed_image` (optional): File upload  
- `thumbnail_image` (optional): File upload

**Capture Data JSON Structure:**
```json
{
  "captureId": "string",
  "frameId": "string",
  "captureTimestamp": "number",
  "detectionResults": [
    {
      "class": "string",
      "confidence": "number",
      "bbox": [x, y, width, height]
    }
  ]
}
```

**Response Schema:**
```json
{
  "success": "boolean",
  "message": "string", 
  "object_id": "string",
  "object_type": "capture",
  "server_object_id": "string (optional)",
  "conflicts": ["string"] (optional)
}
```

**cURL Example:**
```bash
curl -X POST "http://localhost:8000/api/v1/sync/capture" \
  -H "Authorization: Bearer <token>" \
  -F "operation_type=create" \
  -F "object_type=capture" \
  -F "object_id=capture-456" \
  -F "frame_id=frame-123" \
  -F "capture_data={\"captureId\":\"capture-456\",\"frameId\":\"frame-123\",\"captureTimestamp\":1640995200000,\"detectionResults\":[]}" \
  -F "original_image=@image.jpg" \
  -F "processed_image=@processed.jpg"
```

**Error Response Examples:**

*Invalid JSON in capture_data (400):*
```json
{
  "detail": "Invalid capture_data JSON format"
}
```

*Parent Frame Missing (200 with success=false):*
```json
{
  "success": false,
  "message": "Parent frame frame-123 not found on server",
  "object_id": "capture-456",
  "object_type": "capture"
}
```

**HTTP Status Codes:**
- `200` - Success (check `success` field in response)
- `400` - Bad Request (invalid JSON format)
- `401` - Unauthorized
- `403` - Forbidden
- `500` - Internal Server Error

**Image Upload Notes:**
- Supported formats: JPEG, PNG, WebP
- Maximum file size: 10MB per image
- Images are stored as binary blobs in the database
- All image parameters are optional

---

### 3. Batch Sync

Synchronize multiple frames and captures in a single request for efficiency.

**Endpoint:** `POST /api/v1/sync/batch`

**Request Schema:**
```json
{
  "requests": [
    {
      "operation_type": "create",
      "object_type": "frame", 
      "object_id": "frame-123",
      "frame_data": { /* frame data */ }
    },
    {
      "operation_type": "create",
      "object_type": "capture",
      "object_id": "capture-456", 
      "frame_id": "frame-123",
      "capture_data": { /* capture data */ }
    }
  ],
  "client_id": "string (optional)"
}
```

**Response Schema:**
```json
{
  "results": [
    {
      "success": "boolean",
      "message": "string",
      "object_id": "string", 
      "object_type": "frame" | "capture",
      "server_object_id": "string (optional)"
    }
  ],
  "total_requested": "number",
  "successful": "number", 
  "failed": "number",
  "errors": [
    {
      "error_code": "string",
      "error_message": "string", 
      "field": "string (optional)"
    }
  ] (optional)
}
```

**Success Response Example:**
```json
{
  "results": [
    {
      "success": true,
      "message": "Frame created successfully",
      "object_id": "frame-123",
      "object_type": "frame",
      "server_object_id": "frame-123"
    },
    {
      "success": true, 
      "message": "Capture created successfully",
      "object_id": "capture-456",
      "object_type": "capture",
      "server_object_id": "capture-456"
    }
  ],
  "total_requested": 2,
  "successful": 2,
  "failed": 0,
  "errors": null
}
```

**Partial Failure Response Example:**
```json
{
  "results": [
    {
      "success": true,
      "message": "Frame created successfully", 
      "object_id": "frame-123",
      "object_type": "frame"
    },
    {
      "success": false,
      "message": "Processing error: Invalid capture data",
      "object_id": "capture-456", 
      "object_type": "capture"
    }
  ],
  "total_requested": 2,
  "successful": 1,
  "failed": 1,
  "errors": [
    {
      "error_code": "PROCESSING_ERROR",
      "error_message": "Invalid capture data"
    }
  ]
}
```

**Batch Processing Notes:**
- Maximum 50 items per batch request
- Each item is processed independently
- Partial failures are supported (some items succeed, others fail)
- Processing stops on first error within an item
- Order of processing matches request order

---

### 4. Health Check

Simple health check endpoint for monitoring sync service availability.

**Endpoint:** `GET /api/v1/sync/health`

**Authentication:** Not required

**Response:**
```json
{
  "status": "healthy",
  "service": "sync", 
  "timestamp": 1640995200000
}
```

**HTTP Status Codes:**
- `200` - Service is healthy
- `503` - Service unavailable (if health checks fail)

---

### 5. Sync Statistics

Get synchronization statistics for the current authenticated user.

**Endpoint:** `GET /api/v1/sync/stats`

**Response:**
```json
{
  "user_id": "user-123",
  "frames_synced": 0,
  "captures_synced": 0,
  "last_sync": null,
  "pending_sync_items": 0
}
```

**HTTP Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `403` - Forbidden

**Note:** This endpoint currently returns placeholder data. In a full implementation, it would query actual sync status from the database.

---

## Data Models

### Frame Data Structure

```typescript
interface FrameData {
  frameId: string;              // Client-generated UUID
  modelNumber: string;          // Equipment model identifier
  machineSerialNumber: string;  // Equipment serial number
  inspectorName: string;        // Inspector display name
  inspectorId?: string;         // Inspector user ID (optional)
  creationTimestamp: number;    // Unix timestamp in milliseconds
  lastModifiedTimestamp: number; // Unix timestamp in milliseconds
  status: "active" | "completed" | "archived";
  captureCount: number;         // Number of captures in this frame
  metadata?: object;            // Additional frame metadata
}
```

### Capture Data Structure

```typescript
interface CaptureData {
  captureId: string;           // Client-generated UUID
  frameId: string;             // Parent frame ID
  captureTimestamp: number;    // Unix timestamp in milliseconds
  detectionResults: DetectionResult[]; // AI detection results
}

interface DetectionResult {
  class: string;              // Detected object class
  confidence: number;         // Confidence score (0-1)
  bbox: [number, number, number, number]; // [x, y, width, height]
}
```

### Sync Operation Types

```typescript
type SyncOperationType = "create" | "update" | "delete";
type SyncObjectType = "frame" | "capture";
```

---

## Error Handling

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `SYNC_FAILED` | General sync operation failure | 200 |
| `PROCESSING_ERROR` | Error processing request data | 200 |
| `INVALID_JSON` | Malformed JSON in request | 400 |
| `UNAUTHORIZED` | Invalid or missing auth token | 401 |
| `FORBIDDEN` | Insufficient permissions | 403 |
| `INTERNAL_ERROR` | Server-side error | 500 |

### Error Response Format

```json
{
  "detail": "string",           // Error description
  "error_code": "string",       // Error code (optional)
  "field": "string"             // Field causing error (optional)
}
```

---

## Performance Considerations

### Request Optimization

1. **Batch Operations**: Use `/batch` endpoint for multiple items to reduce network overhead
2. **Image Compression**: Compress images before upload to reduce transfer time
3. **Incremental Sync**: Only sync changed data using timestamps
4. **Connection Pooling**: Reuse HTTP connections for multiple requests

### Rate Limiting

- No explicit rate limiting implemented
- Consider implementing based on usage patterns
- Monitor server resources during peak sync operations

### Database Performance

The sync service includes optimized database indexes for common query patterns:

```sql
-- Frame lookups by ID
CREATE INDEX idx_frames_id ON frames(frame_id);

-- Capture lookups by frame and timestamp
CREATE INDEX idx_captures_frame_timestamp ON captures(frame_id, capture_timestamp);

-- Sync status queries
CREATE INDEX idx_captures_sync_status ON captures(sync_status);
```

---

## Usage Examples

### JavaScript Client Example

```javascript
class SyncClient {
  constructor(baseUrl, authToken) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  async syncFrame(frameData, operationType = 'create') {
    const response = await fetch(`${this.baseUrl}/api/v1/sync/frame`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`
      },
      body: JSON.stringify({
        operation_type: operationType,
        object_type: 'frame',
        object_id: frameData.frameId,
        frame_data: frameData
      })
    });

    if (!response.ok) {
      throw new Error(`Sync failed: ${response.status}`);
    }

    return await response.json();
  }

  async syncCaptureWithImages(captureData, images, operationType = 'create') {
    const formData = new FormData();
    formData.append('operation_type', operationType);
    formData.append('object_type', 'capture');
    formData.append('object_id', captureData.captureId);
    formData.append('frame_id', captureData.frameId);
    formData.append('capture_data', JSON.stringify(captureData));
    
    if (images.original) {
      formData.append('original_image', images.original);
    }
    if (images.processed) {
      formData.append('processed_image', images.processed);
    }
    if (images.thumbnail) {
      formData.append('thumbnail_image', images.thumbnail);
    }

    const response = await fetch(`${this.baseUrl}/api/v1/sync/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.authToken}`
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Capture sync failed: ${response.status}`);
    }

    return await response.json();
  }

  async syncBatch(requests) {
    const response = await fetch(`${this.baseUrl}/api/v1/sync/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`
      },
      body: JSON.stringify({
        requests: requests
      })
    });

    if (!response.ok) {
      throw new Error(`Batch sync failed: ${response.status}`);
    }

    return await response.json();
  }
}
```

### Python Client Example

```python
import aiohttp
import json
from typing import Optional, Dict, Any

class SyncClient:
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url
        self.auth_token = auth_token
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={'Authorization': f'Bearer {self.auth_token}'}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def sync_frame(self, frame_data: Dict[str, Any], operation_type: str = 'create'):
        payload = {
            'operation_type': operation_type,
            'object_type': 'frame',
            'object_id': frame_data['frameId'],
            'frame_data': frame_data
        }
        
        async with self.session.post(
            f'{self.base_url}/api/v1/sync/frame',
            json=payload
        ) as response:
            if response.status != 200:
                raise Exception(f'Sync failed: {response.status}')
            return await response.json()

    async def health_check(self):
        async with self.session.get(f'{self.base_url}/api/v1/sync/health') as response:
            return await response.json()
```

---

## Security Considerations

### Authentication Security

1. **Token Management**: Access tokens expire after 30 minutes
2. **HTTPS Only**: Use HTTPS in production environments
3. **Token Storage**: Store tokens securely on client side
4. **Refresh Tokens**: Implement token refresh flow for long-running sessions

### Data Validation

1. **Input Sanitization**: All inputs are validated before processing
2. **File Upload Security**: Image uploads are validated for type and size
3. **SQL Injection Prevention**: Using SQLAlchemy ORM with parameterized queries
4. **JSON Schema Validation**: Request data validated against Pydantic schemas

### Authorization

1. **User Isolation**: Users can only sync their own data (enforced by client_id)
2. **Role-Based Access**: Admin users have additional permissions
3. **Resource Ownership**: Frame/capture ownership verified before operations

---

## Development and Testing

### Running the Server

```bash
cd backend
uv sync                                    # Install dependencies
uv run uvicorn app.main:app --reload      # Start development server
```

### Testing Endpoints

```bash
# Run automated tests
cd backend
python test_sync.py

# Manual testing with curl
curl -X GET http://localhost:8000/api/v1/sync/health

# Test with authentication
curl -X POST http://localhost:8000/api/v1/sync/frame \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"operation_type":"create","object_type":"frame","object_id":"test-123","frame_data":{}}'
```

### Environment Variables

```bash
# Required for production
export SECRET_KEY="your-secret-key-here"
export REFRESH_SECRET_KEY="your-refresh-secret-key-here"

# Optional configuration
export ACCESS_TOKEN_EXPIRE_MINUTES=30
export REFRESH_TOKEN_EXPIRE_DAYS=7
```

---

## Future Enhancements

### Planned Features

1. **Conflict Resolution**: Enhanced conflict detection and resolution strategies
2. **Incremental Sync**: Delta synchronization to reduce data transfer
3. **Compressed Transfers**: Automatic compression for large payloads
4. **Sync Scheduling**: Configurable sync intervals and retry policies
5. **Audit Logging**: Detailed sync operation logging for troubleshooting

### API Versioning

The current API is version 1 (`/api/v1/sync`). Future versions will:
- Maintain backward compatibility when possible
- Use semantic versioning for breaking changes
- Provide migration guides for major version updates

---

## Support and Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check that auth token is valid and not expired
2. **Parent Frame Missing**: Ensure frames are synced before their captures
3. **Large Image Uploads**: Consider compressing images or using batch uploads
4. **Network Timeouts**: Implement retry logic with exponential backoff

### Debug Information

Enable debug logging by setting log level to DEBUG in the server configuration. This will provide detailed information about:
- Request validation
- Database operations
- Error stack traces
- Performance metrics

### Contact

For technical support or questions about the sync API, please refer to the project documentation or contact the development team.