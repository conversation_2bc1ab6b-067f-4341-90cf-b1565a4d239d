# IndexedDB Sync Operations Documentation
## Weld Defect Detection System

This document provides comprehensive documentation of how IndexedDB handles sync operations in the weld defect detection system, covering the complete data flow from local storage to server synchronization.

## Table of Contents
1. [IndexedDB Schema for Sync Operations](#schema)
2. [Sync Queue Lifecycle and State Management](#lifecycle)
3. [Data Operations that Trigger Sync](#triggers)
4. [Transaction Patterns and Atomic Operations](#transactions)
5. [Error Handling and Recovery Mechanisms](#error-handling)
6. [Performance Considerations and Optimizations](#performance)

---

## IndexedDB Schema for Sync Operations {#schema}

### Database Structure
The system uses a single IndexedDB database named `weld-detection-db` with three main object stores designed for offline-first operation with background synchronization.

```typescript
interface WeldDetectionDB extends DBSchema {
  frames: {
    key: string;           // frameId (UUID)
    value: Frame;
    indexes: {
      'by-machine-inspector': [string, string];  // [machineSerialNumber, inspectorName]
      'by-sync-status': string;                  // syncStatus
    };
  };
  captures: {
    key: string;           // captureId (UUID)
    value: Capture;
    indexes: {
      'by-frame': string;                        // frameId
      'by-sync-status': string;                  // syncStatus
    };
  };
  syncQueue: {
    key: number;           // queueId (auto-increment)
    value: SyncQueueItem;
    indexes: {
      'by-status-priority': [string, number];    // [status, priority]
    };
  };
}
```

### Core Data Types

#### Frame (Detection Session)
```typescript
interface Frame {
  frameId: string;                    // UUID primary key
  modelNumber: string;                // Equipment model
  machineSerialNumber: string;        // Machine identifier
  inspectorName: string;              // Inspector identifier
  creationTimestamp: number;          // Creation time (ms since epoch)
  lastModifiedTimestamp: number;      // Last modification time
  status: 'active' | 'completed' | 'archived';
  captureCount: number;               // Number of captures in this session
  metadata?: Record<string, unknown>; // Additional session data
  syncStatus: 'synced' | 'pending' | 'conflict';  // Sync state
  lastSyncedAt?: number;              // Last successful sync timestamp
}
```

#### Capture (Individual Detection)
```typescript
interface Capture {
  captureId: string;                  // UUID primary key
  frameId: string;                    // Foreign key to frames
  captureTimestamp: number;           // Capture time
  originalImageBlob?: Blob;           // Original image data
  processedImageBlob?: Blob;          // Processed image with annotations
  thumbnailBlob?: Blob;               // Small thumbnail for UI
  detectionResults: DetectionResult[]; // AI detection results
  syncStatus: 'synced' | 'pending' | 'conflict';
  syncVersion: number;                // Optimistic concurrency control
  lastSyncAttempt?: number;           // Last sync attempt timestamp
}
```

#### SyncQueueItem (Sync Operation)
```typescript
interface SyncQueueItem {
  queueId?: number;                   // Auto-incremented by IndexedDB
  operationType: 'create' | 'update' | 'delete';  // Operation to perform
  objectType: 'frame' | 'capture';   // Type of object being synced
  objectId: string;                   // ID of the object to sync
  priority: number;                   // Sync priority (higher = more urgent)
  createdAt: number;                  // Queue entry creation time
  attemptCount: number;               // Number of sync attempts
  lastAttempt?: number;               // Last attempt timestamp
  status: 'pending' | 'processing' | 'completed' | 'failed';
}
```

### Index Design and Performance

#### Primary Indexes
- **frames**: Primary key on `frameId` (UUID)
- **captures**: Primary key on `captureId` (UUID)  
- **syncQueue**: Primary key on `queueId` (auto-increment)

#### Secondary Indexes
- **frames.by-machine-inspector**: Compound index for session grouping and filtering
- **frames.by-sync-status**: Fast filtering of frames by sync state
- **captures.by-frame**: Foreign key relationship for frame → captures
- **captures.by-sync-status**: Fast filtering of captures by sync state
- **syncQueue.by-status-priority**: Efficient priority-based queue processing

---

## Sync Queue Lifecycle and State Management {#lifecycle}

### State Transitions

```mermaid
graph TD
    A[Operation Triggered] --> B[Add to Queue: pending]
    B --> C[Sync Manager Picks Up: processing]
    C --> D{Sync Successful?}
    D -->|Yes| E[Mark as completed & Remove]
    D -->|No| F{Max Retries?}
    F -->|No| G[Increment attempts & back to pending]
    F -->|Yes| H[Mark as failed]
    G --> C
    H --> I[Manual retry or remain failed]
    I --> J[Reset to pending with zero attempts]
    J --> C
```

### Queue Priority System

The sync queue uses a priority-based system to ensure critical operations are processed first:

```typescript
// Priority levels used in the system
const SYNC_PRIORITIES = {
  FRAME_CREATE: 10,    // Highest - new sessions
  FRAME_DELETE: 8,     // High-medium - cleanup operations
  CAPTURE_CREATE: 7,   // Medium-high - new detections
  FRAME_UPDATE: 5,     // Medium - session updates
  CAPTURE_UPDATE: 4,   // Medium-low - detection updates
  CAPTURE_DELETE: 3    // Lowest - cleanup operations
};
```

### Queue Processing Algorithm

1. **Retrieval**: Get pending items ordered by `[status, priority]` index
2. **Sorting**: Secondary sort by priority (desc) then creation time (asc)
3. **Processing**: Sequential processing with configurable batch size (default: 50)
4. **Retry Logic**: Exponential backoff with maximum retry limit (3 attempts)

---

## Data Operations that Trigger Sync Queue Additions {#triggers}

### Frame Operations

#### Frame Creation
```typescript
// Location: frameOperations.ts:createFrame()
// Triggers: High priority sync queue entry (priority: 10)
await db.add('syncQueue', {
  operationType: 'create',
  objectType: 'frame',
  objectId: frameId,
  priority: 10,
  createdAt: now,
  attemptCount: 0,
  status: 'pending'
});
```

#### Frame Updates
```typescript
// Location: frameOperations.ts:updateFrame()
// Triggers: Medium priority sync (priority: 5) only if not being marked as synced
if (updates.syncStatus !== 'synced') {
  updatedFrame.syncStatus = 'pending';
  // Add to sync queue only for user updates, not sync updates
  await db.add('syncQueue', {
    operationType: 'update',
    objectType: 'frame',
    objectId: frameId,
    priority: 5,
    // ...
  });
}
```

#### Frame Deletion
```typescript
// Location: frameOperations.ts:deleteFrame()
// Triggers: High-medium priority sync (priority: 8)
// Also cascades to delete all related captures
await tx.objectStore('syncQueue').add({
  operationType: 'delete',
  objectType: 'frame',
  objectId: frameId,
  priority: 8,
  // ...
});
```

### Capture Operations

#### Capture Creation
```typescript
// Location: captureOperations.ts:createCapture()
// Triggers: Medium-high priority sync (priority: 7)
await tx.objectStore('syncQueue').add({
  operationType: 'create',
  objectType: 'capture',
  objectId: captureId,
  priority: 7,
  // ...
});
```

#### Capture Updates
```typescript
// Location: captureOperations.ts:updateCapture()
// Triggers: Medium priority sync (priority: 4) with version increment
if (updates.syncStatus !== 'synced') {
  updatedCapture.syncStatus = 'pending';
  updatedCapture.syncVersion = capture.syncVersion + 1;
  // Add to sync queue
}
```

#### Capture Deletion
```typescript
// Location: captureOperations.ts:deleteCapture()
// Triggers: Lower priority sync (priority: 3)
await tx.objectStore('syncQueue').add({
  operationType: 'delete',
  objectType: 'capture',
  objectId: captureId,
  priority: 3,
  // ...
});
```

---

## Transaction Patterns and Atomic Operations {#transactions}

### Multi-Store Atomic Transactions

The system ensures data consistency through carefully designed atomic transactions:

#### Frame Deletion with Cascade
```typescript
// Atomic transaction across frames, captures, and syncQueue
const tx = db.transaction(['frames', 'captures', 'syncQueue'], 'readwrite');

// Delete the frame
await tx.objectStore('frames').delete(frameId);

// Delete all captures for this frame using cursor
const captureIndex = tx.objectStore('captures').index('by-frame');
let cursor = await captureIndex.openCursor(frameId);
while (cursor) {
  await cursor.delete();
  cursor = await cursor.continue();
}

// Add to sync queue
await tx.objectStore('syncQueue').add({...});

// Commit atomically
await tx.done;
```

#### Capture Creation with Frame Update
```typescript
// Two-phase commit pattern to avoid transaction timeouts
// Phase 1: Generate thumbnail outside transaction (async operation)
let thumbnailBlob: Blob | undefined;
try {
  thumbnailBlob = await createThumbnail(processedImageBlob);
} catch (error) {
  console.error('Failed to create thumbnail:', error);
}

// Phase 2: Atomic database operations
const tx = db.transaction(['captures', 'syncQueue'], 'readwrite');
await tx.objectStore('captures').add(capture);
await tx.objectStore('syncQueue').add({...});
await tx.done;

// Phase 3: Update frame capture count (separate transaction)
await incrementCaptureCount(frameId);
```

### Transaction Performance Patterns

#### Avoiding Transaction Timeouts
- **Pre-compute expensive operations**: Generate thumbnails before opening transactions
- **Minimize transaction scope**: Keep transactions focused on database operations only
- **Separate long-running operations**: File I/O and computations happen outside transactions

#### Optimistic Concurrency Control
```typescript
// Version-based conflict resolution for captures
const updatedCapture: Capture = {
  ...capture,
  ...updates,
  syncVersion: capture.syncVersion + 1  // Increment version on each update
};
```

---

## Error Handling and Recovery Mechanisms {#error-handling}

### Sync Error Handling Strategy

#### Retry Logic with Exponential Backoff
```typescript
private async handleSyncError(item: SyncQueueItem, errorMessage: string): Promise<void> {
  const newAttemptCount = item.attemptCount + 1;
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1 second base delay

  if (newAttemptCount >= MAX_RETRIES) {
    // Max retries reached, mark as failed
    await updateSyncQueueItem({
      ...item,
      status: 'failed',
      attemptCount: newAttemptCount,
      lastAttempt: Date.now()
    });
  } else {
    // Exponential backoff: 1s, 2s, 4s, max 30s
    const backoffDelay = Math.min(
      RETRY_DELAY * Math.pow(2, newAttemptCount - 1), 
      30000
    );
    
    await updateSyncQueueItem({
      ...item,
      status: 'pending',
      attemptCount: newAttemptCount,
      lastAttempt: Date.now() + backoffDelay
    });
  }
}
```

#### Sync Status Recovery
```typescript
// Recovery operations for failed sync items
export async function retryFailedItems(): Promise<void> {
  const failedItems = await getFailedSyncItems();
  
  for (const item of failedItems) {
    await updateSyncQueueItem({
      ...item,
      status: 'pending',
      attemptCount: 0,        // Reset attempt count
      lastAttempt: Date.now()
    });
  }
}
```

### Transaction Error Recovery

#### Database Connection Management
```typescript
// Robust database connection with error recovery
export async function initDB(): Promise<IDBPDatabase<WeldDetectionDB>> {
  return openDB<WeldDetectionDB>(DB_NAME, DB_VERSION, {
    upgrade(db, oldVersion, newVersion, transaction) {
      // Schema creation and migration logic
    },
    blocked() {
      console.warn('Database opening blocked. Another tab may have the database open.');
    },
    blocking() {
      console.warn('This tab is blocking a newer version of the database.');
    },
    terminated() {
      console.error('Database connection was terminated unexpectedly.');
      dbPromise = null;  // Reset connection cache
    }
  });
}
```

#### Graceful Degradation
```typescript
// Offline-first with graceful server sync
export async function loadSessionsWithFallback(): Promise<Frame[]> {
  try {
    // Always load from IndexedDB first
    const localFrames = await getAllFrames();

    // Start background sync without blocking UI
    if (navigator.onLine) {
      setTimeout(() => {
        sessionSyncService.processPendingSync().catch(() => {
          // Silently ignore sync errors in background
        });
      }, 100);
    }

    return localFrames;
  } catch (error) {
    console.error('Error loading sessions:', error);
    return []; // Return empty array rather than failing
  }
}
```

---

## Performance Considerations and Optimizations {#performance}

### Indexing Strategy

#### Query-Optimized Indexes
```typescript
// Composite index for common query pattern
'by-machine-inspector': [string, string]  // For filtering by machine + inspector
'by-status-priority': [string, number]    // For priority queue processing
```

#### Index Usage Patterns
```typescript
// Efficient range queries using compound indexes
const items = await index.getAll(
  IDBKeyRange.bound(['pending', 0], ['pending', Number.MAX_SAFE_INTEGER])
);
```

### Memory Management

#### Blob Storage Optimization
```typescript
// Efficient metadata-only queries (excludes large blobs)
export async function getCaptureMetadataByFrameId(frameId: string): 
  Promise<Omit<Capture, 'originalImageBlob' | 'processedImageBlob'>[]> {
  
  const results: Omit<Capture, 'originalImageBlob' | 'processedImageBlob'>[] = [];
  let cursor = await index.openCursor(frameId);
  
  while (cursor) {
    const { originalImageBlob, processedImageBlob, ...metadata } = cursor.value;
    results.push(metadata);  // Exclude large blob data
    cursor = await cursor.continue();
  }
  
  return results;
}
```

#### Thumbnail Generation for Performance
```typescript
// Generate small thumbnails for UI performance
async function createThumbnail(imageBlob: Blob, maxSize: number = 100): Promise<Blob> {
  // Resize image to small thumbnail for list views
  // Quality: 0.7 JPEG compression for size optimization
}
```

### Sync Performance Optimizations

#### Batch Processing with Rate Limiting
```typescript
// Process sync items in batches with delays
for (const item of pendingItems) {
  await this.processSyncItem(item);
  await this.delay(100);  // Small delay to avoid overwhelming server
}
```

#### Priority-Based Processing
```typescript
// Process high-priority items first
const sortedItems = items.sort((a, b) => {
  if (a.priority !== b.priority) {
    return b.priority - a.priority; // Higher priority first
  }
  return a.createdAt - b.createdAt; // Older items first within same priority
});
```

### Database Size Management

#### Cleanup Operations
```typescript
// Periodic cleanup of completed sync items
export async function clearCompletedSyncItems(): Promise<void> {
  const completedItems = await index.getAll(
    IDBKeyRange.bound(['completed', 0], ['completed', Number.MAX_SAFE_INTEGER])
  );
  
  await Promise.all(
    completedItems.map(item => {
      if (item.queueId !== undefined) {
        return tx.store.delete(item.queueId);
      }
    })
  );
}
```

#### Archive Strategy
```typescript
// Archive old completed sessions to manage database size
export async function archiveOldSessions(olderThanDays: number = 30): Promise<number> {
  const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
  
  for (const frame of frames) {
    if (frame.status === 'completed' && frame.lastModifiedTimestamp < cutoffTime) {
      frame.status = 'archived';
      frame.syncStatus = 'pending';  // Trigger sync of archived status
    }
  }
}
```

### Best Practices Summary

1. **Offline-First Design**: All operations work locally first, sync happens in background
2. **Atomic Transactions**: Use multi-store transactions for data consistency
3. **Priority-Based Sync**: Critical operations (frame creation) sync before updates
4. **Exponential Backoff**: Retry failed syncs with increasing delays
5. **Graceful Degradation**: System works offline, syncs when possible
6. **Memory Efficiency**: Separate metadata queries from blob storage
7. **Index Optimization**: Compound indexes for common query patterns
8. **Cleanup Strategy**: Remove completed sync items and archive old data

This architecture ensures robust offline operation with reliable background synchronization while maintaining excellent performance and user experience.