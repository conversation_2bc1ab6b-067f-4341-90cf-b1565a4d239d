# Final Client-to-Server Sync Implementation Plan

## Overview
Implement basic one-way sync (Client → Server) using existing code structure. Focus on simplicity and incrementally building on what's already there.

---

# Phase 1: Backend Sync Foundation (2 days)

## Day 1: Create Sync Service Layer

### 📝 NEW FILE: `backend/app/services/sync_service.py`
**Purpose:** Handle sync business logic
**Functions to implement:**
- `sync_frame_from_client(frame_data, client_id)`
- `sync_capture_from_client(capture_data, client_id)`
- `check_duplicate_frame(frame_id, client_id)`
- `check_duplicate_capture(capture_id, client_id)`

### 📝 NEW FILE: `backend/app/schemas/sync.py`
**Purpose:** Sync-specific data models
**Models needed:**
- `SyncFrameRequest`
- `SyncCaptureRequest` 
- `SyncResponse`
- `SyncStatus`

## Day 2: Add Sync Endpoints

### 🔧 MODIFY: `backend/app/routers/frames.py`
**Keep:** All existing endpoints unchanged
**Add:** 
- `@router.post("/sync")` endpoint
- Import sync service
- Handle sync-specific errors

### 🔧 MODIFY: `backend/app/routers/captures.py`
**Keep:** All existing capture CRUD operations  
**Add:**
- `@router.post("/sync")` endpoint
- Handle image data in sync context
- Sync-specific validation

### 🔧 MODIFY: `backend/app/services/frame_service.py`
**Keep:** All existing methods
**Add:**
- `sync_frame_from_client()` method
- Duplicate detection for frames

### 🔧 MODIFY: `backend/app/services/capture_service.py`
**Keep:** All existing capture operations
**Add:**
- `sync_capture_from_client()` method
- Handle sync metadata updates

---

# Phase 2: Frontend Sync Manager (2 days)

## Day 3: Core Sync Infrastructure

### 📝 NEW FILE: `frontend/src/lib/sync/syncManager.ts`
**Purpose:** Process IndexedDB sync queue
**Functions to implement:**
- `processSyncQueue()` - Main queue processor
- `syncSingleFrame(frame)` - Sync one frame
- `syncSingleCapture(capture)` - Sync one capture
- `handleSyncError(error, item)` - Basic error handling
- `updateSyncStatus(itemId, status)` - Update local status

### 🔧 MAJOR REWRITE: `frontend/src/lib/services/sessionSync.ts`
**Keep:**
- `sessionSyncService` class structure
- `checkServerConnectivity()` method
- API URL configuration
- `convertApiFrameToLocal()` and `convertLocalFrameToApi()` methods

**Replace/Remove:**
- ❌ `loadSessionsWithFallback()` - too complex for Phase 1
- ❌ `syncWithServerInBackground()` - placeholder only
- ❌ Server fallback logic in session loading

**Add:**
- `syncFrameToServer(frame)` - Send frame to server
- `syncCaptureToServer(capture)` - Send capture to server
- `processPendingSync()` - Process sync queue
- Basic retry logic (3 attempts max)

## Day 4: Sync Integration

### 🔧 MODIFY: `frontend/src/components/home/<USER>
**Keep:** All existing connection display
**Add:**
- Manual sync button
- Sync status indicator (syncing/success/failed)
- Integration with new sync manager

### 🔧 MINOR MODIFY: `frontend/src/lib/db/frameOperations.ts`
**Keep:** All existing operations
**Verify:** Frame creation sets `syncStatus: 'pending'`
**Ensure:** Sync queue items are created properly

### 🔧 MINOR MODIFY: `frontend/src/lib/db/captureOperations.ts`
**Keep:** All existing operations  
**Verify:** Capture creation sets `syncStatus: 'pending'`
**Ensure:** Sync queue items are created properly

---

# Phase 3: UI Integration & Basic Features (1 day)

## Day 5: User Interface & Testing

### 📝 NEW FILE: `frontend/src/components/sync/SyncStatus.tsx`
**Purpose:** Reusable sync status component
**Features:**
- Show sync state (pending/syncing/synced/failed)
- Manual retry button for failed items
- Sync progress indication

### 🔧 MINOR MODIFY: `frontend/src/components/detection/HistoryPanel.tsx`
**Keep:** All existing functionality
**Add:** 
- Sync status indicators for each capture
- Integration with SyncStatus component

### 🔧 MINOR MODIFY: `frontend/src/app/detection/page.tsx`
**Keep:** All existing detection functionality
**Add:**
- Sync status in session banner
- Connection to sync manager

### 🔧 MINOR MODIFY: `frontend/src/components/layout/header.tsx`
**Keep:** All existing header elements
**Add:**
- Global sync status indicator
- Manual sync trigger button

---

# Implementation Priority & Timeline

## Week 1: Core Sync (Days 1-5)
**Goal:** Basic sync working end-to-end

### Critical Path:
1. **Day 1:** Backend sync service
2. **Day 2:** Backend sync endpoints  
3. **Day 3:** Frontend sync manager
4. **Day 4:** Sync integration
5. **Day 5:** UI integration & testing

### Success Criteria:
✅ Manual sync button works
✅ Frames sync from client to server
✅ Captures sync from client to server
✅ Basic error handling with retries
✅ UI shows sync status

## Week 2: Polish & Auto-Sync (Days 6-7)
**Goal:** Smooth user experience

### Additional Features:
6. **Day 6:** Auto-sync triggers (network restore, app focus)
7. **Day 7:** Periodic sync (every 5 minutes) + error improvements

---

# Files Summary

## 📝 New Files to Create (5 files):
1. `backend/app/services/sync_service.py`
2. `backend/app/schemas/sync.py`
3. `frontend/src/lib/sync/syncManager.ts`
4. `frontend/src/components/sync/SyncStatus.tsx`

## 🔧 Files to Modify (8 files):
1. `backend/app/routers/frames.py` - Add sync endpoint
2. `backend/app/routers/captures.py` - Add sync endpoint
3. `backend/app/services/frame_service.py` - Add sync method
4. `backend/app/services/capture_service.py` - Add sync method
5. `frontend/src/lib/services/sessionSync.ts` - Major rewrite
6. `frontend/src/components/home/<USER>
7. `frontend/src/components/detection/HistoryPanel.tsx` - Add sync status
8. `frontend/src/app/detection/page.tsx` - Add sync integration

## ✅ Files to Keep Unchanged (All others):
- Database models (already sync-ready)
- IndexedDB operations (already create sync queue)
- Core detection components
- Authentication system
- Camera and capture pipeline

---

# Key Design Decisions

**Simple & Incremental:**
- One-way sync only (client → server)
- Process one item at a time (no batching)
- Manual sync as primary method
- Basic retry logic (3 attempts)

**Leverage Existing Code:**
- Use existing sync queue in IndexedDB
- Use existing sync status fields
- Build on existing API structure
- Minimal changes to working components

**Fail Safely:**
- Don't block UI during sync
- Continue processing queue if items fail
- Preserve local data always
- Clear error messages

**Observable:**
- Sync status visible to users
- Manual sync button available
- Good debugging information
- Progress indication

This plan delivers a working sync system in 5-7 days while respecting the existing codebase and keeping complexity minimal.