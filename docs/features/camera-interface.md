# Camera Interface & WebRTC Implementation

The camera interface provides a comprehensive WebRTC-based camera system with real-time capture capabilities, automatic image processing, and seamless integration with the AI detection pipeline.

## Overview

The camera system delivers a professional-grade inspection interface featuring:
- **WebRTC Camera Access** with high-resolution support
- **Real-time Image Capture** with automatic processing
- **Center-crop Square Framing** for consistent detection input
- **Integrated AI Processing** with live detection overlay
- **Automatic Image Storage** to IndexedDB with sync capabilities

## Core Components

### CameraPanel (`CameraPanel.tsx`)

**Primary Features:**
- **WebRTC Integration**: Direct browser camera access via getUserMedia
- **AI Model Management**: Automatic model loading with progress tracking
- **Real-time Capture**: One-click image capture with full processing pipeline
- **Performance Monitoring**: Live inference timing and metrics display
- **Error Handling**: Comprehensive error recovery and user guidance

**Camera Configuration:**
```typescript
const stream = await navigator.mediaDevices.getUserMedia({
  video: { 
    width: { ideal: 1280 }, 
    height: { ideal: 720 } 
  }
});
```

**Processing Pipeline:**
1. **Video Stream Setup**: WebRTC connection to camera
2. **Live Preview**: Real-time video display
3. **Capture Trigger**: User-initiated image capture
4. **Center Cropping**: Automatic square crop for AI processing
5. **AI Detection**: Real-time object detection
6. **Result Overlay**: Detection visualization
7. **Data Storage**: Save to IndexedDB with sync queue

### CameraControls (`CameraControls.tsx`)

**Control Interface:**
- **Camera Toggle**: Start/stop camera with visual feedback
- **Capture Button**: Large, accessible capture trigger
- **Processing Indicator**: Visual feedback during AI processing
- **Status Display**: Real-time camera and processing status

**UI Elements:**
```typescript
interface ControlStates {
  cameraActive: boolean;     // Camera stream status
  isCapturing: boolean;      // Processing state
  modelLoaded: boolean;      // AI model availability
  hasPermission: boolean;    // Camera permission status
}
```

### FrameManager (`FrameManager.tsx`)

**Session Management:**
- **Frame Information**: Display current session details
- **Capture Count**: Real-time capture statistics
- **Timestamp Display**: Session creation and modification times
- **ID Tracking**: Unique frame identifier management

## WebRTC Implementation

### Camera Access & Permissions

**Permission Handling:**
```typescript
// Request camera permissions with error handling
try {
  const stream = await navigator.mediaDevices.getUserMedia({
    video: { width: { ideal: 1280 }, height: { ideal: 720 } }
  });
  // Handle successful access
} catch (err) {
  // Handle permission denied, device unavailable, etc.
}
```

**Stream Management:**
- **Auto-start**: Camera activates on component mount
- **Clean Shutdown**: Proper stream termination on unmount
- **Error Recovery**: Automatic retry with user guidance
- **Permission Prompts**: Clear user instructions for camera access

### Video Processing

**Resolution Handling:**
- **Dynamic Detection**: Automatic resolution detection from video stream
- **Optimal Sizing**: Ideal resolution request with fallback support
- **Aspect Ratio**: Maintains original aspect ratio for preview
- **Center Cropping**: Square crop extraction for AI processing

**Stream States:**
```typescript
interface CameraStates {
  inactive: "Camera not started";
  loading: "Requesting camera access";
  active: "Live video stream";
  error: "Permission denied or device unavailable";
  processing: "Capturing and processing image";
}
```

## Image Capture Pipeline

### Capture Process

**Complete Workflow:**
1. **Pre-validation**: Check camera, model, and session status
2. **Frame Extraction**: Capture current video frame to canvas
3. **Center Cropping**: Extract square region from center
4. **Dual Processing**: Create both original and AI-ready versions
5. **AI Detection**: Run inference on resized image
6. **Result Overlay**: Apply detection visualization
7. **Blob Creation**: Convert processed images to storage format
8. **Database Storage**: Save to IndexedDB with metadata
9. **UI Update**: Refresh capture history and sync status

**Image Processing Steps:**
```typescript
// 1. Capture video frame to canvas
canvas.width = size;
canvas.height = size;
ctx.drawImage(video, xOffset, yOffset, size, size, 0, 0, size, size);

// 2. Create 640x640 version for AI
const resizedCanvas = resizeImageTo640(canvas);

// 3. Run detection
const { detections, metrics } = await performDetection(resizedCanvas);

// 4. Create processed version with overlay
const processedCanvas = createDetectionCanvas(resizedCanvas, detections);

// 5. Convert to blobs for storage
const originalBlob = await canvasToBlob(canvas);
const processedBlob = await canvasToBlob(processedCanvas);
```

### Image Quality & Optimization

**Format Optimization:**
- **JPEG Compression**: Configurable quality (default: 90%)
- **Size Management**: Optimized file sizes for storage
- **Resolution Control**: Consistent 640x640 AI processing size
- **Original Preservation**: Full-resolution original image stored

**Performance Considerations:**
- **Async Processing**: Non-blocking capture operation
- **Progress Feedback**: Real-time user feedback during processing
- **Error Recovery**: Graceful handling of processing failures
- **Memory Management**: Automatic cleanup of temporary canvases

## User Interface Features

### Visual Feedback

**Camera Status Indicators:**
- **Live Preview**: Real-time video display
- **Resolution Display**: Current stream resolution overlay
- **Model Loading**: Progress indicator with percentage
- **Processing State**: Visual feedback during capture

**Status Overlays:**
```typescript
interface StatusOverlays {
  cameraInactive: "Start camera prompt with retry button";
  modelLoading: "AI model loading progress with spinner";
  modelError: "Error message with reload option";
  processing: "Capture in progress indicator";
  performanceMetrics: "Optional inference timing display";
}
```

### Responsive Design

**Layout Adaptation:**
- **Flexible Sizing**: Adapts to available screen space
- **Mobile Optimization**: Touch-friendly controls
- **Aspect Ratio**: Maintains camera preview proportions
- **Control Positioning**: Accessible control placement

**Accessibility Features:**
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Keyboard Navigation**: Full keyboard control support
- **High Contrast**: Clear visual indicators
- **Large Targets**: Touch-accessible control sizes

## Integration Points

### Session Context Integration

**Session Management:**
```typescript
const { frameId } = useSession();
// Ensures captures are associated with active session
```

**Data Flow:**
- **Session Validation**: Verify active session before capture
- **Frame Association**: Link captures to current detection frame
- **Metadata Inclusion**: Session info embedded in capture data

### AI Detection Integration

**Real-time Processing:**
```typescript
// Integrated AI pipeline
const { detections, metrics } = await detect(canvas, model, {
  confidenceThreshold: 0.25,
  iouThreshold: 0.45,
  maxDetections: 100,
  inputSize: 640
});
```

**Performance Monitoring:**
- **Inference Timing**: Real-time performance metrics
- **Memory Usage**: TensorFlow.js memory tracking
- **Error Handling**: AI processing error recovery

### Database & Sync Integration

**Automatic Storage:**
```typescript
// Save to IndexedDB with sync queue
const capture = await createCapture(
  frameId,
  originalBlob,    // Original captured image
  processedBlob,   // AI-processed with detections
  detections       // Detection metadata
);
```

**Sync Coordination:**
- **Queue Management**: Automatic sync queue updates
- **Status Tracking**: Real-time sync status display
- **Conflict Resolution**: Handles offline/online transitions

## Performance Optimization

### Camera Stream Optimization

**Stream Management:**
- **Efficient Preview**: Optimized video element rendering
- **Memory Conservation**: Proper stream cleanup
- **Frame Rate Control**: Optimal preview frame rates
- **Bandwidth Management**: Efficient video processing

### Processing Optimization

**Capture Efficiency:**
```typescript
// Optimized capture pipeline
- Single canvas operations
- Minimal memory allocation
- Efficient blob conversion
- Async processing with progress
```

**Resource Management:**
- **Canvas Reuse**: Minimize canvas creation
- **Memory Cleanup**: Automatic resource disposal
- **Background Processing**: Non-blocking operations
- **Error Boundaries**: Prevent UI freezing

## Configuration Options

### Camera Settings

**Stream Configuration:**
```typescript
const cameraConfig = {
  video: {
    width: { ideal: 1280, min: 640 },
    height: { ideal: 720, min: 480 },
    facingMode: 'environment',  // Rear camera preferred
    frameRate: { ideal: 30, max: 60 }
  }
};
```

### Processing Settings

**Capture Configuration:**
```typescript
const captureConfig = {
  jpegQuality: 0.9,           // Image compression quality
  targetSize: 640,            // AI processing resolution
  showMetrics: false,         // Performance metrics display
  autoCapture: false          // Automatic capture mode
};
```

## Error Handling & Recovery

### Camera Errors

**Common Error Scenarios:**
- **Permission Denied**: Clear user guidance for enabling camera
- **Device Unavailable**: Detection and alternative suggestions
- **Stream Interruption**: Automatic reconnection attempts
- **Multiple Tab Conflicts**: Resource sharing guidance

**Error Recovery:**
```typescript
// Comprehensive error handling
try {
  await startCamera();
} catch (error) {
  if (error.name === 'NotAllowedError') {
    // Permission denied - show instructions
  } else if (error.name === 'NotFoundError') {
    // No camera available - show alternatives
  } else {
    // Other errors - general guidance
  }
}
```

### Processing Errors

**AI Processing Failures:**
- **Model Loading**: Retry mechanisms with user feedback
- **Inference Errors**: Graceful degradation and retry
- **Memory Issues**: Automatic cleanup and user guidance
- **Network Problems**: Offline mode with local processing

## Browser Compatibility

### Supported Browsers

**Full Support:**
- Chrome 90+ (optimal performance)
- Firefox 90+ (full features)
- Safari 14+ (iOS/macOS support)
- Edge 90+ (Chromium-based)

**Feature Requirements:**
- WebRTC (getUserMedia)
- Canvas API
- Web Workers (for processing)
- IndexedDB (for storage)
- ES2020 support

### Mobile Considerations

**iOS Support:**
- Safari iOS 14.3+ required
- Camera permission handling
- Orientation lock support
- Memory optimization

**Android Support:**
- Chrome Mobile 90+
- Camera switching support
- Touch gesture optimization
- Performance adaptation

## Troubleshooting Guide

### Common Issues

**Camera Access Problems:**
1. **Permission Denied**: Check browser settings, reload page
2. **No Camera Found**: Verify device connection, try different browser
3. **Stream Failure**: Close other applications using camera
4. **Poor Quality**: Check lighting, clean lens, adjust position

**Performance Issues:**
1. **Slow Capture**: Close other tabs, check device memory
2. **Blurry Images**: Ensure stable holding, good lighting
3. **AI Processing Slow**: Verify model loading, check device specs
4. **Storage Errors**: Check available storage, clear browser cache

### Debug Tools

**Built-in Diagnostics:**
- Performance metrics display
- Camera stream status indicators  
- Model loading progress
- Error message details
- Console logging for developers