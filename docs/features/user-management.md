# User Management & Authentication System

The user management system provides a comprehensive JWT-based authentication framework with role-based access control, secure password management, and seamless integration with the offline-first architecture.

## Overview

The authentication system delivers enterprise-grade security featuring:
- **JWT Token Authentication** with access and refresh tokens
- **Role-Based Access Control** (Admin and Inspector roles)
- **Secure Password Management** with bcrypt hashing
- **Automatic Token Refresh** with seamless user experience
- **Password Reset System** with email verification
- **Session Management** with cross-tab synchronization

## Authentication Architecture

### JWT Token System (`auth.py`)

**Dual Token Strategy:**
- **Access Tokens**: Short-lived (30 minutes) for API authentication
- **Refresh Tokens**: Long-lived (7 days) for token renewal
- **Reset Tokens**: Time-limited (60 minutes) for password reset

**Token Configuration:**
```python
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7
RESET_TOKEN_EXPIRE_MINUTES = 60
ALGORITHM = "HS256"
```

**Security Features:**
- **Separate Secrets**: Different keys for access and refresh tokens
- **Token Types**: Type validation to prevent token misuse
- **Expiration Handling**: Automatic expiry validation
- **Nonce Generation**: Secure random values for reset tokens

### Client-Side Auth Context (`AuthContext.tsx`)

**State Management:**
```typescript
interface AuthContextType {
  user: User | null;                    // Current user data
  tokens: AuthTokens | null;            // JWT tokens
  isLoading: boolean;                   // Authentication status
  isAuthenticated: boolean;             // Login state
  login: (username, password) => Promise<void>;
  register: (userData) => Promise<void>;
  logout: () => void;
  refreshTokens: () => Promise<boolean>;
  updateProfile: (data) => Promise<void>;
  changePassword: (current, new) => Promise<void>;
}
```

**Persistent Storage:**
- **LocalStorage**: Token and user data persistence
- **Cookie Integration**: Middleware support for SSR
- **Cross-Tab Sync**: Shared authentication state
- **Secure Cleanup**: Complete data removal on logout

## User Roles & Permissions

### Role Definitions

**Inspector Role:**
- Create and manage detection sessions
- Capture and process images
- View own detection history
- Update personal profile
- Basic sync operations

**Admin Role:**
- All Inspector permissions
- Manage other users
- View system-wide analytics
- Access administrative functions
- Advanced sync monitoring

### Role-Based Access Control

**Frontend Guards (`AuthGuard.tsx`):**
```typescript
// Require specific roles
<AuthGuard requiredRoles={['admin']}>
  <AdminPanel />
</AuthGuard>

// Require any authenticated user
<AuthenticatedOnly>
  <DetectionInterface />
</AuthenticatedOnly>
```

**Backend Authorization:**
```python
@router.get("/admin-only")
async def admin_endpoint(
    current_user: User = Depends(get_current_user_admin)
):
    # Admin-only functionality
```

### Permission Enforcement

**Granular Permissions:**
- **Data Access**: Users can only access their own data
- **API Endpoints**: Role-based endpoint restrictions
- **UI Components**: Conditional rendering based on role
- **Sync Operations**: User-scoped synchronization

## User Registration & Management

### Registration Process (`auth.py`)

**Registration Flow:**
1. **Input Validation**: Username, email, and password validation
2. **Duplicate Check**: Prevent duplicate usernames and emails
3. **Password Hashing**: Secure bcrypt password storage
4. **User Creation**: Database entry with default role
5. **Auto-Login**: Immediate authentication after registration

**Validation Rules:**
```python
class UserRegister(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: str = Field(..., min_length=1, max_length=100)
    password: str = Field(..., min_length=8)
    role: Optional[UserRole] = UserRole.INSPECTOR
```

### User Repository (`user_repository.py`)

**Database Operations:**
- **User Creation**: Secure user account setup
- **Credential Validation**: Username and email uniqueness
- **Password Management**: Secure hash generation and verification
- **Profile Updates**: Safe user data modification
- **Last Login Tracking**: Activity monitoring

**Security Features:**
```python
# Secure password hashing
hashed_password = get_password_hash(password)

# Password verification
verify_password(plain_password, hashed_password)

# Last login tracking
await user_repo.update_last_login(user.user_id)
```

## Authentication Flow

### Login Process

**Client-Side Login (`AuthContext.tsx`):**
```typescript
const login = async (username: string, password: string) => {
  // 1. Send credentials to server
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    body: formData
  });

  // 2. Store tokens
  const tokens = await response.json();
  setStoredTokens(tokens);

  // 3. Fetch user profile
  const userResponse = await fetch('/api/v1/auth/me', {
    headers: { Authorization: `Bearer ${tokens.access_token}` }
  });

  // 4. Update state
  const userData = await userResponse.json();
  setUser(userData);
};
```

**Server-Side Authentication:**
```python
# Validate credentials
user = await authenticate_user(db, username, password)

# Generate tokens
access_token = create_access_token(data={"sub": user.username})
refresh_token = create_refresh_token(data={"sub": user.username})

# Return token response
return {
    "access_token": access_token,
    "token_type": "bearer",
    "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    "refresh_token": refresh_token
}
```

### Automatic Token Refresh

**Proactive Refresh Strategy:**
```typescript
useEffect(() => {
  if (!tokens) return;

  const expiresAt = Date.now() + (tokens.expires_in * 1000);
  const refreshTime = expiresAt - (5 * 60 * 1000); // 5 minutes before expiry
  
  const timeout = setTimeout(() => {
    refreshTokens();
  }, refreshTime - Date.now());

  return () => clearTimeout(timeout);
}, [tokens]);
```

**Automatic Retry Logic:**
```typescript
// Retry failed requests with refreshed tokens
if (response.status === 401 && tokens?.refresh_token) {
  const refreshSuccess = await refreshTokens();
  if (refreshSuccess) {
    // Retry original request with new token
    return fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${newTokens.access_token}`
      }
    });
  }
}
```

## Password Security

### Password Hashing (`password.py`)

**Bcrypt Implementation:**
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

**Security Features:**
- **Salt Generation**: Automatic salt generation for each password
- **Cost Factor**: Configurable bcrypt cost for security/performance balance
- **Timing Attack Protection**: Constant-time password verification
- **Password Validation**: Minimum length and complexity requirements

### Password Reset System

**Reset Flow:**
1. **Email Request**: User submits email for password reset
2. **Token Generation**: Server creates secure reset token
3. **Email Delivery**: Reset link sent to user's email
4. **Token Validation**: Server validates reset token on submission
5. **Password Update**: New password hash stored securely

**Reset Token Security:**
```python
def create_reset_token(email: str) -> str:
    expire = datetime.utcnow() + timedelta(minutes=RESET_TOKEN_EXPIRE_MINUTES)
    to_encode = {
        "email": email,
        "exp": expire,
        "type": "reset",
        "nonce": secrets.token_urlsafe(16)  # Prevent replay attacks
    }
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
```

### Password Change

**Authenticated Password Change:**
```typescript
const changePassword = async (currentPassword: string, newPassword: string) => {
  const response = await makeAuthenticatedRequest('/api/v1/auth/change-password', {
    method: 'POST',
    body: JSON.stringify({
      current_password: currentPassword,
      new_password: newPassword
    })
  });
  
  if (!response.ok) {
    throw new Error('Password change failed');
  }
};
```

## Session Management

### Cross-Tab Synchronization

**Shared Authentication State:**
- **localStorage Events**: Sync login/logout across tabs
- **Cookie Synchronization**: Middleware support for auth state
- **Event Broadcasting**: Real-time auth state updates
- **Automatic Cleanup**: Logout propagation across all tabs

**Implementation:**
```typescript
// Listen for storage changes across tabs
window.addEventListener('storage', (e) => {
  if (e.key === 'auth_tokens') {
    if (e.newValue === null) {
      // Logout detected in another tab
      logout();
    } else {
      // Login detected in another tab
      const tokens = JSON.parse(e.newValue);
      setTokens(tokens);
    }
  }
});
```

### Session Persistence

**Secure Storage Strategy:**
```typescript
// Store in both localStorage and httpOnly cookies
const setStoredTokens = (tokens: AuthTokens | null) => {
  if (tokens) {
    localStorage.setItem('auth_tokens', JSON.stringify(tokens));
    document.cookie = `auth_tokens=${encodedTokens}; path=/; max-age=${tokens.expires_in}`;
  } else {
    localStorage.removeItem('auth_tokens');
    document.cookie = 'auth_tokens=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
  }
};
```

## Middleware Integration

### Route Protection (`middleware.ts`)

**Authentication Middleware:**
```typescript
export function middleware(request: NextRequest) {
  const token = request.cookies.get('auth_tokens')?.value;
  
  if (!token) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  // Validate token and continue
  return NextResponse.next();
}
```

**Protected Routes:**
```typescript
export const config = {
  matcher: [
    '/detection/:path*',
    '/profile/:path*',
    '/admin/:path*'
  ]
};
```

### Protected Route Components (`ProtectedRoute.tsx`)

**Route-Level Protection:**
```typescript
export default function ProtectedRoute({ children }: { children: ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}
```

## User Interface Components

### Authentication Forms

**Login Form (`login/page.tsx`):**
- **Responsive Design**: Mobile-friendly authentication interface
- **Form Validation**: Client-side validation with server feedback
- **Error Handling**: Clear error messages and recovery guidance
- **Loading States**: Visual feedback during authentication

**Registration Form (`register/page.tsx`):**
- **Role Selection**: Choose between Inspector and Admin roles
- **Password Confirmation**: Double-entry password validation
- **Terms Acceptance**: Legal compliance checkbox
- **Auto-Login**: Seamless transition after registration

### Profile Management (`profile/page.tsx`)

**User Profile Features:**
- **Profile Editing**: Update name, email, and other details
- **Password Change**: Secure password update interface
- **Activity History**: Login history and session information
- **Security Settings**: Token management and security options

## Security Best Practices

### Token Security

**Token Handling:**
- **HttpOnly Cookies**: Secure token storage for SSR
- **SameSite Policy**: CSRF protection
- **Secure Flag**: HTTPS-only cookie transmission
- **Token Rotation**: Regular refresh token rotation

### Authentication Security

**Security Measures:**
- **Rate Limiting**: Prevent brute force attacks
- **Input Validation**: Comprehensive input sanitization
- **Error Handling**: Generic error messages to prevent enumeration
- **Session Timeout**: Automatic logout on inactivity

### Password Security

**Password Policies:**
- **Minimum Length**: 8 character minimum requirement
- **Complexity**: Character variety recommendations
- **History**: Prevent password reuse
- **Expiration**: Optional password expiration policies

## API Endpoints

### Authentication Endpoints (`auth.py`)

**Core Auth Operations:**
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/register` - New user registration
- `POST /api/v1/auth/refresh` - Token refresh
- `GET /api/v1/auth/me` - Current user profile

**Password Management:**
- `POST /api/v1/auth/forgot-password` - Password reset request
- `POST /api/v1/auth/reset-password` - Password reset confirmation
- `POST /api/v1/auth/change-password` - Authenticated password change

### User Management Endpoints (`users.py`)

**User Operations:**
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update user profile
- `GET /api/v1/users` - List users (admin only)
- `DELETE /api/v1/users/{user_id}` - Delete user (admin only)

## Default Users

### System Bootstrap

**Default Accounts:**
```python
# Admin account
username: "admin"
password: "admin123"
role: "admin"
email: "<EMAIL>"

# Inspector account
username: "inspector1"
password: "password123"
role: "inspector"
email: "<EMAIL>"
```

**Production Deployment:**
- Change default passwords immediately
- Create proper admin accounts
- Disable or remove default accounts
- Configure proper email settings

## Integration with Sync System

### User-Scoped Synchronization

**Data Isolation:**
```python
# Sync operations are user-scoped
client_id = f"user_{current_user.user_id}"
sync_service.sync_frame_from_client(sync_request, client_id)
```

**Permission Enforcement:**
- **Data Access**: Users can only sync their own data
- **Cross-User Prevention**: Strict user isolation in sync operations
- **Admin Override**: Admin users can access all data for maintenance

## Troubleshooting Guide

### Common Authentication Issues

**Login Problems:**
1. **Invalid Credentials**: Verify username and password
2. **Account Inactive**: Check user activation status
3. **Token Expired**: Automatic refresh should handle this
4. **Network Issues**: Check server connectivity

**Session Issues:**
1. **Automatic Logout**: Check token expiration and refresh logic
2. **Cross-Tab Problems**: Verify localStorage synchronization
3. **Permission Denied**: Confirm user role and permissions
4. **Profile Updates Fail**: Check authentication state and server logs

### Security Incident Response

**Suspected Compromise:**
1. **Immediate Logout**: Force logout all sessions
2. **Token Revocation**: Invalidate all existing tokens
3. **Password Reset**: Force password change for affected users
4. **Audit Logging**: Review access logs for suspicious activity

**System Maintenance:**
1. **Token Cleanup**: Regular cleanup of expired tokens
2. **User Audit**: Review active user accounts and permissions
3. **Security Updates**: Regular dependency updates and patches
4. **Monitoring**: Continuous monitoring of authentication patterns