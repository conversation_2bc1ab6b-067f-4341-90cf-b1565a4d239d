# AI Detection System

The weld defect detection system features a complete client-side AI detection engine powered by TensorFlow.js and YOLOv8n, providing real-time object detection capabilities with optimized performance.

## Overview

The AI detection system runs entirely in the browser using TensorFlow.js, enabling:
- **Real-time object detection** with YOLOv8n model
- **Client-side inference** with no server dependency
- **WebGL acceleration** for optimal performance
- **Automatic device optimization** based on capabilities
- **Comprehensive performance monitoring** and metrics

## Core Components

### Model Loading & Management (`modelLoader.ts`)

**Features:**
- **Adaptive Model Selection**: Automatically selects optimal model size based on device capabilities
- **Progressive Loading**: Real-time progress tracking with callback support
- **Intelligent Caching**: In-memory model caching to prevent redundant downloads
- **WebGL Backend**: Automatic WebGL initialization with CPU fallback
- **Model Warmup**: Pre-computation to ensure consistent inference performance

**Supported Models:**
- `yolov8n` (640x640) - Full precision for desktop/high-end devices
- `yolov8n-small` (320x320) - Optimized for mobile/low-end devices

**Device Detection Logic:**
```typescript
// Automatic optimization based on:
- Mobile device detection (iPhone/iPad/Android)
- CPU core count (≤2 cores = low-end)
- Available memory (≤4GB = limited)
```

### Detection Engine (`detector.ts`)

**Core Detection Pipeline:**
1. **Image Preprocessing**: Aspect-ratio preserving resize with padding
2. **Model Inference**: TensorFlow.js graph model execution
3. **Post-processing**: Coordinate transformation and confidence filtering
4. **Non-Maximum Suppression**: Overlapping detection removal

**Detection Options:**
- `confidenceThreshold` (default: 0.25) - Minimum detection confidence
- `iouThreshold` (default: 0.45) - IoU threshold for NMS
- `maxDetections` (default: 100) - Maximum number of detections
- `inputSize` (default: 640) - Model input resolution

**Performance Monitoring:**
```typescript
interface PerformanceMetrics {
  preprocessTime: number;    // Image preprocessing duration
  inferenceTime: number;     // Model inference duration  
  postprocessTime: number;   // Results processing duration
  totalTime: number;         // End-to-end processing time
}
```

### Image Preprocessing (`preprocessUtils.ts`)

**Preprocessing Pipeline:**
- **Aspect Ratio Preservation**: Scales images while maintaining proportions
- **Center Padding**: Adds black padding to create square input
- **Normalization**: Converts pixel values to [0,1] range
- **Tensor Conversion**: Optimized canvas-to-tensor conversion

**Key Functions:**
- `preprocess()` - Main preprocessing with ratio tracking
- `canvasToTensor()` - Direct tensor conversion without resizing
- `resizeImage()` - Simple image resizing utility

### Visualization & Rendering (`renderUtils.ts`)

**Detection Visualization:**
- **Bounding Box Rendering**: Color-coded detection boxes
- **Confidence Display**: Percentage confidence scores
- **Class Label Overlay**: Object class names
- **Semi-transparent Fill**: Subtle detection highlighting

**Rendering Options:**
```typescript
interface RenderOptions {
  lineWidth?: number;          // Bounding box line thickness
  fontSize?: number;           // Label text size
  confidenceThreshold?: number; // Minimum confidence to display
  showConfidence?: boolean;     // Show confidence percentages
  showLabels?: boolean;        // Show class labels
  scaleFactor?: {x: number; y: number}; // Coordinate scaling
}
```

## Object Detection Classes

The system uses the COCO dataset with 80 object classes including:

**Common Objects:**
- Person, car, truck, bicycle, motorcycle
- Chair, table, laptop, phone, book
- Bottle, cup, bowl, knife, fork

**Detection Colors:**
- Each class assigned unique color from 20-color palette
- Consistent color mapping across sessions
- High contrast colors for visibility

## Performance Optimization

### Model Optimization
- **WebGL Backend**: Leverages GPU acceleration when available
- **Tensor Memory Management**: Automatic cleanup to prevent memory leaks
- **Model Warmup**: Eliminates first-inference delays
- **Batch Processing**: Optimized for single-image inference

### Device Adaptation
```typescript
// Performance tiers based on device capabilities
if (isMobile || isLowEndDevice || hasLimitedMemory) {
  // Use smaller model (320x320)
  return { modelName: 'yolov8n-small', inputSize: 320 };
} else {
  // Use full model (640x640)  
  return { modelName: 'yolov8n', inputSize: 640 };
}
```

### Memory Management
- **Automatic Tensor Disposal**: Prevents memory accumulation
- **Model Caching**: Reuses loaded models across sessions
- **Memory Monitoring**: Real-time memory usage tracking

## Integration with Detection Pipeline

### Camera Integration
```typescript
// Real-time detection in camera feed
const { detections, metrics } = await detect(canvas, model, {
  confidenceThreshold: 0.25,
  iouThreshold: 0.45,
  maxDetections: 100,
  inputSize: 640
});
```

### Image Processing Workflow
1. **Capture**: Camera frame captured to canvas
2. **Crop**: Center-crop to square aspect ratio
3. **Resize**: Scale to 640x640 for detection
4. **Detect**: Run AI inference
5. **Render**: Overlay detection results
6. **Store**: Save both original and processed images

### Performance Metrics Display
- **Real-time Performance**: Live inference timing
- **Breakdown Timing**: Separate preprocessing/inference/postprocessing times
- **Toggle Display**: User-controllable performance overlay

## Error Handling & Fallbacks

### Model Loading Errors
- **Network Failures**: Graceful error messages with retry options
- **Invalid Models**: Model validation with clear error reporting
- **Backend Failures**: Automatic fallback from WebGL to CPU

### Detection Errors
- **Memory Issues**: Automatic cleanup and user notification
- **Invalid Input**: Input validation with user guidance
- **Performance Issues**: Device-specific optimization suggestions

## Configuration & Customization

### Detection Parameters
```typescript
const detectionConfig = {
  confidenceThreshold: 0.25,  // Adjust detection sensitivity
  iouThreshold: 0.45,         // Control overlap handling
  maxDetections: 100,         // Limit detection count
  inputSize: 640             // Model resolution
};
```

### Visual Customization
```typescript
const renderConfig = {
  lineWidth: 3,              // Bounding box thickness
  fontSize: 16,              // Label text size
  showConfidence: true,      // Display confidence scores
  showLabels: true          // Display class names
};
```

## Future Enhancements

### Planned Features
- **Custom Model Support**: Ability to load custom-trained models
- **Multi-model Ensemble**: Combine multiple models for improved accuracy
- **Real-time Video Processing**: Continuous detection on video streams
- **Detection Tracking**: Object tracking across frames

### Performance Improvements
- **WebAssembly Backend**: Even faster CPU inference
- **Model Quantization**: Smaller models with maintained accuracy
- **Progressive Enhancement**: Adaptive quality based on performance
- **Background Processing**: Web Worker integration for non-blocking inference

## Technical Requirements

### Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 90+, Safari 14+
- **WebGL Support**: Required for optimal performance
- **Canvas API**: Essential for image processing
- **ES2020 Features**: Modern JavaScript support required

### Hardware Recommendations
- **Desktop**: 8GB+ RAM, dedicated GPU preferred
- **Mobile**: 4GB+ RAM, recent processor
- **Network**: Stable connection for initial model download
- **Storage**: 50MB+ available for model caching

## Troubleshooting

### Common Issues
- **Model Loading Fails**: Check network connection, try reload
- **Slow Performance**: Verify WebGL support, close other tabs
- **Memory Errors**: Refresh page, check available RAM
- **Inconsistent Results**: Ensure proper lighting, stable camera

### Debug Tools
- **Performance Metrics**: Built-in timing displays
- **Memory Monitor**: TensorFlow.js memory tracking
- **Console Logging**: Detailed detection pipeline logs
- **Error Boundaries**: Graceful error handling and recovery