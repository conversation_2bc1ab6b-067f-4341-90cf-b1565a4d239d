# Deployment Guide

This guide covers production deployment strategies, configurations, and best practices for the Weld Defect Detection System.

## 🚀 Deployment Overview

The system supports multiple deployment strategies:
- **Single Server Deployment** - Simple setup for small-medium scale
- **Container Deployment** - Docker/Docker Compose for scalability
- **Cloud Deployment** - AWS, GCP, Azure for enterprise scale
- **Edge Deployment** - Local/offline environments

## 📋 Pre-Deployment Checklist

### **Security Checklist**
- [ ] Change default admin credentials
- [ ] Generate secure JWT secret keys
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up firewall rules
- [ ] Configure CORS for production domains
- [ ] Review file upload limits
- [ ] Enable security headers

### **Performance Checklist**
- [ ] Configure database indexes
- [ ] Set up static file serving (CDN)
- [ ] Configure caching headers
- [ ] Optimize image compression settings
- [ ] Test AI model performance
- [ ] Configure resource limits

### **Monitoring Checklist**
- [ ] Set up health check endpoints
- [ ] Configure logging aggregation
- [ ] Set up error tracking
- [ ] Configure performance monitoring
- [ ] Set up backup procedures
- [ ] Test disaster recovery

## 🖥️ Single Server Deployment

### **System Requirements**

**Minimum Requirements:**
- **OS:** Ubuntu 20.04+ or similar Linux distribution
- **CPU:** 4 cores (8 recommended)
- **RAM:** 8GB (16GB recommended) 
- **Storage:** 50GB SSD (100GB+ recommended)
- **Network:** 100Mbps+ internet connection

**Recommended Requirements:**
- **OS:** Ubuntu 22.04 LTS
- **CPU:** 8+ cores with GPU support
- **RAM:** 32GB+
- **Storage:** 200GB+ NVMe SSD
- **Network:** 1Gbps+ with redundancy

### **Installation Steps**

#### 1. System Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install system dependencies
sudo apt install -y \
    nginx \
    postgresql \
    redis-server \
    python3.12 \
    python3.12-venv \
    nodejs \
    npm \
    git \
    curl \
    certbot \
    python3-certbot-nginx

# Install uv for Python package management
curl -LsSf https://astral.sh/uv/install.sh | sh
source $HOME/.cargo/env
```

#### 2. Application Setup

```bash
# Create application user
sudo useradd -m -s /bin/bash weld-detection
sudo usermod -aG sudo weld-detection

# Switch to application user
sudo su - weld-detection

# Clone repository
git clone https://github.com/your-org/weld-detection.git
cd weld-detection

# Setup backend
cd backend
uv venv
source .venv/bin/activate
uv sync --frozen

# Setup frontend
cd ../frontend
npm ci --only=production
npm run build
```

#### 3. Database Setup

```bash
# Configure PostgreSQL (recommended for production)
sudo -u postgres psql

CREATE DATABASE weld_detection;
CREATE USER weld_user WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE weld_detection TO weld_user;
\q

# Run migrations
cd backend
source .venv/bin/activate
alembic upgrade head
```

#### 4. Environment Configuration

```bash
# Backend environment
cat > backend/.env << EOF
# Security
SECRET_KEY=$(openssl rand -hex 32)
ALGORITHM=HS256

# Database
DATABASE_URL=postgresql+asyncpg://weld_user:secure_password_here@localhost/weld_detection

# JWT Settings
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# File Settings
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=.jpg,.jpeg,.png

# CORS
CORS_ORIGINS=https://your-domain.com

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/weld-detection/app.log
EOF

# Frontend environment
cat > frontend/.env.production << EOF
NEXT_PUBLIC_API_BASE_URL=https://your-domain.com/api
EOF
```

#### 5. System Services

```bash
# Backend systemd service
sudo tee /etc/systemd/system/weld-detection-api.service << EOF
[Unit]
Description=Weld Detection API
After=network.target postgresql.service

[Service]
Type=simple
User=weld-detection
WorkingDirectory=/home/<USER>/weld-detection/backend
Environment=PATH=/home/<USER>/weld-detection/backend/.venv/bin
ExecStart=/home/<USER>/weld-detection/backend/.venv/bin/uvicorn app.main:app --host 127.0.0.1 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Frontend systemd service
sudo tee /etc/systemd/system/weld-detection-web.service << EOF
[Unit]
Description=Weld Detection Web
After=network.target

[Service]
Type=simple
User=weld-detection
WorkingDirectory=/home/<USER>/weld-detection/frontend
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Enable and start services
sudo systemctl enable weld-detection-api weld-detection-web
sudo systemctl start weld-detection-api weld-detection-web
```

#### 6. Nginx Configuration

```bash
# Nginx site configuration
sudo tee /etc/nginx/sites-available/weld-detection << 'EOF'
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=1r/s;

server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Frontend (Next.js)
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # API Backend
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # File Upload Endpoints
    location /api/v1/captures/ {
        limit_req zone=upload burst=5 nodelay;
        
        client_max_body_size 20M;
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 600s;
        proxy_connect_timeout 75s;
    }
    
    # Static Files (AI Models)
    location /models/ {
        alias /home/<USER>/weld-detection/frontend/public/models/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Health Check
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:8000/health;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/weld-detection /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### 7. SSL Certificate

```bash
# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🐳 Container Deployment

### **Docker Setup**

#### Dockerfile - Backend

```dockerfile
# backend/Dockerfile
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies
RUN uv sync --frozen --no-cache

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000

CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Dockerfile - Frontend

```dockerfile
# frontend/Dockerfile
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM node:22-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  database:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: weld_detection
      POSTGRES_USER: weld_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U weld_user -d weld_detection"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: postgresql+asyncpg://weld_user:${DB_PASSWORD}@database/weld_detection
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY}
      CORS_ORIGINS: https://${DOMAIN}
    volumes:
      - ./data:/app/data
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      NEXT_PUBLIC_API_BASE_URL: https://${DOMAIN}/api
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - static_files:/var/www/static:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  static_files:

networks:
  default:
    driver: bridge
```

#### Environment Configuration

```bash
# .env
DB_PASSWORD=secure_db_password_here
SECRET_KEY=secure_jwt_secret_here
DOMAIN=your-domain.com

# Production deployment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## ☁️ Cloud Deployment

### **AWS Deployment**

#### ECS with Fargate

```yaml
# aws-task-definition.json
{
  "family": "weld-detection",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/weld-detection-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "postgresql+asyncpg://username:password@rds-endpoint/weld_detection"
        }
      ],
      "secrets": [
        {
          "name": "SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:weld-detection/jwt-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/weld-detection",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -f http://localhost:8000/health || exit 1"
        ],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    },
    {
      "name": "frontend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/weld-detection-frontend:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NEXT_PUBLIC_API_BASE_URL",
          "value": "https://api.your-domain.com"
        }
      ]
    }
  ]
}
```

#### CloudFormation Template

```yaml
# infrastructure.yml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'Weld Detection System Infrastructure'

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]

Resources:
  # VPC and Networking
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub '${Environment}-weld-detection-vpc'

  # RDS Database
  Database:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: !Sub '${Environment}-weld-detection-db'
      DBName: weld_detection
      Engine: postgres
      EngineVersion: '15.3'
      DBInstanceClass: db.t3.micro
      AllocatedStorage: 20
      StorageType: gp2
      StorageEncrypted: true
      MasterUsername: weld_user
      MasterUserPassword: !Ref DatabasePassword
      VPCSecurityGroups:
        - !Ref DatabaseSecurityGroup
      DBSubnetGroupName: !Ref DatabaseSubnetGroup
      BackupRetentionPeriod: 7
      DeletionProtection: true

  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub '${Environment}-weld-detection'
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT

  # Application Load Balancer
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub '${Environment}-weld-detection-alb'
      Type: application
      Scheme: internet-facing
      SecurityGroups:
        - !Ref ALBSecurityGroup
      Subnets:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2

Outputs:
  LoadBalancerDNS:
    Description: DNS name of the load balancer
    Value: !GetAtt LoadBalancer.DNSName
    Export:
      Name: !Sub '${Environment}-weld-detection-alb-dns'
```

### **GCP Deployment**

#### Cloud Run Configuration

```yaml
# cloud-run-backend.yml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: weld-detection-backend
  labels:
    app: weld-detection
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "1"
    spec:
      containerConcurrency: 80
      containers:
      - image: gcr.io/your-project/weld-detection-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-url
              key: url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
```

## 🔧 Environment-Specific Configurations

### **Development Environment**

```bash
# Backend .env.development
SECRET_KEY=dev-secret-key-not-for-production
DATABASE_URL=sqlite+aiosqlite:///./dev_weld_detection.db
ACCESS_TOKEN_EXPIRE_MINUTES=60
CORS_ORIGINS=http://localhost:3000
LOG_LEVEL=DEBUG

# Frontend .env.development
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

### **Staging Environment**

```bash
# Backend .env.staging
SECRET_KEY=staging-secret-key
DATABASE_URL=postgresql+asyncpg://user:pass@staging-db/weld_detection
ACCESS_TOKEN_EXPIRE_MINUTES=30
CORS_ORIGINS=https://staging.your-domain.com
LOG_LEVEL=INFO

# Frontend .env.staging
NEXT_PUBLIC_API_BASE_URL=https://staging-api.your-domain.com
```

### **Production Environment**

```bash
# Backend .env.production
SECRET_KEY=${SECRET_KEY_FROM_VAULT}
DATABASE_URL=${DATABASE_URL_FROM_VAULT}
ACCESS_TOKEN_EXPIRE_MINUTES=15
CORS_ORIGINS=https://your-domain.com
LOG_LEVEL=WARNING
SENTRY_DSN=${SENTRY_DSN}

# Frontend .env.production
NEXT_PUBLIC_API_BASE_URL=https://api.your-domain.com
NEXT_PUBLIC_SENTRY_DSN=${SENTRY_DSN}
```

## 📊 Monitoring and Logging

### **Application Monitoring**

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    ports:
      - "9090:9090"

  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"

  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml

volumes:
  prometheus_data:
  grafana_data:
```

### **Log Aggregation**

```python
# backend/app/logging_config.py
import logging
import sys
from pathlib import Path

def setup_logging():
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(log_format))
    
    # File handler
    log_file = Path("logs/app.log")
    log_file.parent.mkdir(exist_ok=True)
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Application loggers
    app_logger = logging.getLogger("app")
    app_logger.setLevel(logging.INFO)
    
    return app_logger
```

## 🔄 CI/CD Pipeline

### **GitHub Actions**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
          
      - name: Install uv
        run: curl -LsSf https://astral.sh/uv/install.sh | sh
        
      - name: Test Backend
        run: |
          cd backend
          uv sync
          uv run pytest
          
      - name: Test Frontend
        run: |
          cd frontend
          npm ci
          npm run lint
          npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
          
      - name: Deploy to ECS
        run: |
          # Build and push Docker images
          docker build -t weld-detection-backend ./backend
          docker build -t weld-detection-frontend ./frontend
          
          # Tag and push to ECR
          docker tag weld-detection-backend:latest $AWS_ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/weld-detection-backend:latest
          docker push $AWS_ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/weld-detection-backend:latest
          
          # Update ECS service
          aws ecs update-service --cluster production-weld-detection --service backend --force-new-deployment
```

## 📋 Post-Deployment Tasks

### **Verification Steps**

```bash
# Health checks
curl -f https://your-domain.com/health
curl -f https://your-domain.com/api/health

# Database connectivity
curl -f https://your-domain.com/api/health/detailed

# AI model loading
curl -f https://your-domain.com/models/yolov8n_web_model/model.json

# Authentication test
curl -X POST https://your-domain.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### **Performance Testing**

```bash
# Load testing with Apache Bench
ab -n 1000 -c 10 https://your-domain.com/
ab -n 100 -c 5 https://your-domain.com/api/v1/frames/

# Monitor resource usage
htop
iotop
nethogs
```

### **Security Verification**

```bash
# SSL/TLS check
nmap --script ssl-enum-ciphers -p 443 your-domain.com

# Security headers check
curl -I https://your-domain.com/

# Port scan
nmap -sS your-domain.com
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Service Won't Start**
   ```bash
   sudo systemctl status weld-detection-api
   journalctl -u weld-detection-api -f
   ```

2. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Test connection
   psql -h localhost -U weld_user -d weld_detection
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate
   sudo certbot certificates
   
   # Renew certificate
   sudo certbot renew --dry-run
   ```

4. **Performance Issues**
   ```bash
   # Monitor resources
   htop
   iotop
   
   # Check application logs
   tail -f /var/log/weld-detection/app.log
   ```

### **Recovery Procedures**

1. **Database Backup**
   ```bash
   pg_dump -h localhost -U weld_user weld_detection > backup.sql
   ```

2. **Application Backup**
   ```bash
   tar -czf weld-detection-backup.tar.gz /home/<USER>/weld-detection
   ```

3. **Rollback Procedure**
   ```bash
   # Stop services
   sudo systemctl stop weld-detection-api weld-detection-web
   
   # Restore from backup
   cd /home/<USER>
   tar -xzf weld-detection-backup.tar.gz
   
   # Start services
   sudo systemctl start weld-detection-api weld-detection-web
   ```

---

This deployment guide provides comprehensive instructions for deploying the weld defect detection system in various environments. Follow the appropriate section based on your deployment requirements and infrastructure.