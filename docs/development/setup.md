# Development Environment Setup

Comprehensive guide for setting up a development environment for the Weld Defect Detection system.

## Development Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Development Environment                   │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Next.js 15)          Backend (FastAPI)          │
│  ├─ React 19                    ├─ Python 3.12+            │
│  ├─ TypeScript 5                ├─ SQLAlchemy ORM          │
│  ├─ Tailwind CSS v4             ├─ SQLite Database         │
│  ├─ TensorFlow.js (AI/ML)       ├─ JWT Authentication      │
│  ├─ IndexedDB (Offline)         ├─ File Storage            │
│  └─ Hot Reload with Turbopack   └─ Auto-reload with uvicorn │
├─────────────────────────────────────────────────────────────┤
│                    Shared Technologies                      │
│  ├─ WebRTC (Camera Access)                                 │
│  ├─ WebSocket (Real-time Sync)                             │
│  ├─ REST API (HTTP Communication)                          │
│  └─ JSON Web Tokens (Authentication)                       │
└─────────────────────────────────────────────────────────────┘
```

## Development Tools

### Required Tools
- **Code Editor**: VS Code (recommended) with extensions
- **Git**: Version control
- **uv**: Python package management
- **npm**: Node.js package management
- **Browser**: Chrome (for development/debugging)

### Recommended VS Code Extensions
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.isort",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json",
    "ms-python.pylint",
    "ms-typescript.vscode-typescript-next"
  ]
}
```

## Project Structure

```
weld_fast/
├── backend/                      # FastAPI backend
│   ├── app/
│   │   ├── main.py              # Application entry point
│   │   ├── auth.py              # Authentication logic
│   │   ├── database.py          # Database configuration
│   │   ├── dependencies.py      # Dependency injection
│   │   ├── models/              # SQLAlchemy models
│   │   ├── routers/             # API route handlers
│   │   ├── schemas/             # Pydantic schemas
│   │   ├── services/            # Business logic services
│   │   └── utils/               # Utility functions
│   ├── pyproject.toml           # Python dependencies (uv)
│   ├── requirements.txt         # Alternative dependency list
│   ├── test_api.py              # Manual API testing
│   └── test_sync.py             # Sync functionality testing
├── frontend/                     # Next.js frontend
│   ├── src/
│   │   ├── app/                 # Next.js App Router pages
│   │   ├── components/          # React components
│   │   ├── context/             # React Context providers
│   │   ├── hooks/               # Custom React hooks
│   │   ├── lib/                 # Utility libraries
│   │   │   ├── api/             # API client functions
│   │   │   ├── db/              # IndexedDB operations
│   │   │   ├── detection/       # AI/ML detection logic
│   │   │   └── sync/            # Synchronization logic
│   │   └── middleware.ts        # Next.js middleware
│   ├── public/
│   │   └── models/              # TensorFlow.js model files
│   ├── package.json             # Node.js dependencies
│   ├── next.config.ts           # Next.js configuration
│   └── tsconfig.json            # TypeScript configuration
└── docs/                        # Documentation
```

## Backend Development Setup

### Environment Configuration

#### 1. Python Environment with uv
```bash
cd backend

# Sync dependencies (recommended)
uv sync

# Add new dependencies
uv add package-name

# Remove dependencies
uv remove package-name

# Update all dependencies
uv sync --upgrade
```

#### 2. Alternative: Traditional Virtual Environment
```bash
cd backend

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### Development Server Options

#### Primary Method (with uv)
```bash
cd backend
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Alternative Entry Points
```bash
# If main module is in root
uv run uvicorn main:app --reload

# With custom host/port
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8001

# With environment variables
uv run uvicorn app.main:app --reload --env-file .env
```

### Database Development

#### Database Files
- **Development**: `weld_detection.db` (SQLite file)
- **Schema**: Defined in `app/models/database.py`
- **Migrations**: Manual scripts in `app/migrations/`

#### Database Operations
```bash
cd backend

# Reset database (delete and recreate)
rm -f weld_detection.db
uv run uvicorn app.main:app --reload  # Tables created on startup

# View database schema
uv run python -c "
from app.database import engine
from sqlalchemy import inspect
inspector = inspect(engine)
print('Tables:', inspector.get_table_names())
"

# Manual database initialization
uv run python -c "
from app.database import create_tables
from app.auth import create_default_users
from app.database import get_db
import asyncio

async def init():
    await create_tables()
    async for db in get_db():
        await create_default_users(db)
        break

asyncio.run(init())
"
```

### API Development

#### Testing API Endpoints
```bash
cd backend

# Comprehensive API testing
uv run python test_api.py

# Sync functionality testing
uv run python test_sync.py

# Manual endpoint testing with curl
curl -X GET http://localhost:8000/
curl -X GET http://localhost:8000/api/health
```

#### Adding New Endpoints
1. Create schema in `app/schemas/`
2. Add route in `app/routers/`
3. Include router in `app/main.py`
4. Add tests to test files

### Authentication Development

#### Default Users (Created Automatically)
```python
# Admin user
username: "admin"
password: "admin123"
roles: ["admin", "inspector"]

# Inspector user
username: "inspector1"
password: "password123"
roles: ["inspector"]
```

#### JWT Token Testing
```bash
# Get token via API
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=inspector1&password=password123"

# Use token in requests
curl -X GET http://localhost:8000/api/v1/frames/ \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Frontend Development Setup

### Development Environment

#### Package Management
```bash
cd frontend

# Install dependencies
npm install

# Add new dependencies
npm install package-name
npm install --save-dev dev-package-name

# Update dependencies
npm update

# Audit security
npm audit
npm audit fix
```

#### Development Server
```bash
cd frontend

# Start development server with Turbopack (fastest)
npm run dev

# Alternative: Standard Next.js dev server
npm run dev -- --turbo=false

# Custom port
npm run dev -- --port 3001

# Network accessible
npm run dev -- --hostname 0.0.0.0
```

### TypeScript Configuration

#### Key TypeScript Settings (`tsconfig.json`)
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "strict": true,
    "noEmit": true,
    "jsx": "preserve",
    "paths": {
      "@/*": ["./src/*"]  // Import alias
    }
  }
}
```

#### Type Checking
```bash
cd frontend

# Type check without emitting files
npx tsc --noEmit

# Watch mode for continuous checking
npx tsc --noEmit --watch
```

### Styling Development

#### Tailwind CSS v4 Configuration
```javascript
// postcss.config.mjs
export default {
  plugins: {
    '@tailwindcss/postcss': {},
  },
}
```

#### Development Tips
- Use Tailwind CSS IntelliSense extension
- Utility-first approach with component abstractions
- Custom components in `src/components/ui/`

### State Management

#### React Context Providers
```typescript
// Global state management
SessionContext      // Session data and management
AuthContext         // Authentication state
CaptureUpdatesContext // Real-time capture updates
```

#### Custom Hooks
```typescript
useAutoRefresh      // Automatic data refresh
useDebounce         // Debounced input handling
useSyncEvents       // Sync status monitoring
useOrchestratedRefresh // Coordinated refresh operations
```

### IndexedDB Development

#### Database Schema
```typescript
interface WeldDetectionDB {
  frames: FrameRecord;      // Detection sessions
  captures: CaptureRecord;  // Individual detections
  syncQueue: SyncQueueRecord; // Background sync
}
```

#### Development Tools
```bash
# Browser DevTools -> Application -> IndexedDB
# View: WeldDetectionDB database
# Tables: frames, captures, syncQueue
```

### AI/ML Development

#### TensorFlow.js Model
- **Model**: YOLOv8n (optimized for web)
- **Location**: `public/models/yolov8n_web_model/`
- **Format**: TensorFlow.js format with sharded weights

#### Detection Pipeline
```typescript
// Key files for AI/ML development
src/lib/detection/
├── detector.ts        // Main detection logic
├── modelLoader.ts     // Model loading and caching
├── preprocessUtils.ts // Image preprocessing
└── renderUtils.ts     // Result visualization
```

## Development Workflows

### Daily Development Routine

#### 1. Start Development Servers
```bash
# Terminal 1 - Backend
cd backend && uv run uvicorn app.main:app --reload

# Terminal 2 - Frontend
cd frontend && npm run dev

# Terminal 3 - Available for testing/commands
```

#### 2. Code Quality Checks
```bash
# Frontend linting
cd frontend && npm run lint

# Backend code quality (add to pyproject.toml if needed)
cd backend && uv run black . && uv run isort .
```

#### 3. Testing During Development
```bash
# API testing
cd backend && uv run python test_api.py

# Sync testing
cd backend && uv run python test_sync.py
```

### Hot Reload and Development Experience

#### Frontend Hot Reload
- **Turbopack**: Enabled by default with `npm run dev`
- **React Fast Refresh**: Preserves component state
- **TypeScript**: Real-time error checking

#### Backend Auto-reload
- **uvicorn --reload**: Watches Python files
- **Automatic restart**: On code changes
- **Database persistence**: Survives restarts

### Debugging

#### Frontend Debugging
```bash
# Browser DevTools
- React DevTools (Chrome extension)
- Sources tab for breakpoints
- Console for logs
- Network tab for API calls
- Application tab for IndexedDB

# VS Code debugging
- Set breakpoints in TypeScript files
- Use "Next.js" debug configuration
```

#### Backend Debugging
```bash
# VS Code debugging
- Set breakpoints in Python files
- Use "FastAPI" debug configuration

# Manual debugging
uv run python -m pdb app/main.py

# Logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Environment Variables

#### Backend `.env` File
```bash
cd backend
cat > .env << 'EOF'
# Development settings
DEBUG=true
DATABASE_URL=sqlite:///./weld_detection.db
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# CORS for development
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
EOF
```

#### Frontend `.env.local` File
```bash
cd frontend
cat > .env.local << 'EOF'
# API endpoints
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# Development flags
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_ENABLE_LOGGING=true
EOF
```

## Performance Optimization

### Frontend Performance
- **Turbopack**: Faster builds and hot reload
- **Dynamic imports**: Code splitting for components
- **Image optimization**: Next.js built-in optimization
- **IndexedDB**: Client-side caching and offline support

### Backend Performance
- **SQLAlchemy**: Async database operations
- **Connection pooling**: Configured for SQLite
- **Background tasks**: For file processing and sync
- **Caching**: Response caching where appropriate

## Security Considerations

### Development Security
- **JWT tokens**: Use secure secrets in production
- **CORS**: Properly configured for development
- **File uploads**: Size and type restrictions
- **Input validation**: Pydantic schemas for all inputs

### Authentication Flow
1. User logs in via `/api/v1/auth/login`
2. Backend returns JWT access token
3. Frontend stores token in memory (not localStorage)
4. Token included in all authenticated requests
5. Automatic token refresh on expiration

## Next Steps

After setting up your development environment:

1. Read [Testing Guide](testing.md) for testing procedures
2. Review [API Documentation](../api/) for backend endpoints
3. Check [Architecture Documentation](../architecture/) for system design
4. Explore codebase starting with `app/main.py` and `src/app/page.tsx`