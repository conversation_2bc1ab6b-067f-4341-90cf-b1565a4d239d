# Coding Standards

This document outlines the coding standards, conventions, and best practices for the Weld Defect Detection System.

## 🎯 General Principles

### **Code Quality Standards**
- **Readability First:** Code should be self-documenting and easy to understand
- **Consistency:** Follow established patterns throughout the codebase
- **Type Safety:** Use TypeScript/Python type hints extensively
- **Error Handling:** Comprehensive error handling at all levels
- **Performance:** Optimize for performance without sacrificing maintainability

### **Architecture Principles**
- **Offline-First:** All operations must work without network connectivity
- **Separation of Concerns:** Clear boundaries between layers and components
- **Dependency Injection:** Use dependency injection for testability
- **Event-Driven:** Use event systems for loose coupling
- **Immutability:** Prefer immutable data structures where possible

## 🖥️ Frontend Standards (TypeScript/React)

### **File Organization**

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Base UI components (shadcn/ui)
│   ├── auth/            # Authentication components
│   ├── detection/       # Detection-specific components
│   └── common/          # Shared components
├── lib/                 # Core utilities and services
│   ├── db/              # Database operations
│   ├── api/             # API client functions
│   ├── utils/           # Utility functions
│   └── types/           # Type definitions
├── hooks/               # Custom React hooks
├── context/             # React Context providers
└── app/                 # Next.js App Router pages
```

### **Naming Conventions**

```typescript
// Components: PascalCase
export const CameraPanel: React.FC<CameraPanelProps> = ({ ... }) => {

// Hooks: camelCase starting with 'use'
export const useDetectionState = () => {

// Constants: SCREAMING_SNAKE_CASE
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Types/Interfaces: PascalCase
interface DetectionResult {
  id: string;
  confidence: number;
  bbox: BoundingBox;
}

// Files: kebab-case
capture-details.tsx
detection-service.ts
```

### **TypeScript Standards**

```typescript
// ✅ Good: Explicit types
interface CaptureCreateRequest {
  frameId: string;
  originalImageBlob: Blob;
  processedImageBlob: Blob;
  detectionResults: DetectionResult[];
}

// ✅ Good: Generic constraints
interface ApiResponse<T extends Record<string, unknown>> {
  data: T;
  status: 'success' | 'error';
  message?: string;
}

// ✅ Good: Union types for state
type SyncStatus = 'synced' | 'pending' | 'conflict' | 'error';

// ❌ Avoid: Any types
// const data: any = response;

// ✅ Better: Proper typing
const data: ApiResponse<FrameData> = response;
```

### **Component Standards**

```typescript
// ✅ Component template
interface ComponentProps {
  // Required props first
  frameId: string;
  onCapture: (capture: Capture) => void;
  
  // Optional props second
  className?: string;
  disabled?: boolean;
}

export const ComponentName: React.FC<ComponentProps> = ({
  frameId,
  onCapture,
  className,
  disabled = false,
}) => {
  // State hooks first
  const [isLoading, setIsLoading] = useState(false);
  
  // Context hooks
  const { user } = useAuth();
  
  // Custom hooks
  const { captures, addCapture } = useCaptures(frameId);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, [dependency]);
  
  // Event handlers
  const handleCapture = useCallback(async () => {
    try {
      setIsLoading(true);
      // Handler logic
    } catch (error) {
      console.error('Capture failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dependency]);
  
  // Render
  return (
    <div className={cn("base-classes", className)}>
      {/* Component JSX */}
    </div>
  );
};
```

### **State Management Standards**

```typescript
// ✅ Context pattern
interface SessionContextValue {
  currentSession: Frame | null;
  setCurrentSession: (session: Frame | null) => void;
  isLoading: boolean;
  error: string | null;
}

export const SessionContext = createContext<SessionContextValue | null>(null);

// ✅ Custom hook for context
export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSession must be used within SessionProvider');
  }
  return context;
};
```

### **Error Handling Standards**

```typescript
// ✅ Error boundaries
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Component error:', error, errorInfo);
    // Log to monitoring service
  }
}

// ✅ Async error handling
const fetchData = async (): Promise<ApiResponse<Data>> => {
  try {
    const response = await api.getData();
    return response;
  } catch (error) {
    if (error instanceof NetworkError) {
      // Handle network error
      throw new Error('Network connection failed');
    }
    // Re-throw unexpected errors
    throw error;
  }
};
```

## 🐍 Backend Standards (Python/FastAPI)

### **File Organization**

```
backend/
├── app/
│   ├── models/          # SQLAlchemy models
│   ├── schemas/         # Pydantic schemas
│   ├── routers/         # API route handlers
│   ├── services/        # Business logic
│   ├── repositories/    # Data access layer
│   ├── utils/           # Utility functions
│   └── dependencies.py  # FastAPI dependencies
```

### **Naming Conventions**

```python
# Functions and variables: snake_case
def create_capture(capture_data: CaptureCreate) -> Capture:

# Classes: PascalCase
class CaptureService:
    def __init__(self, db: AsyncSession):

# Constants: SCREAMING_SNAKE_CASE
MAX_FILE_SIZE = 10 * 1024 * 1024
DEFAULT_PAGE_SIZE = 20

# Files: snake_case
capture_service.py
frame_repository.py
```

### **Type Annotations**

```python
# ✅ Good: Complete type annotations
from typing import List, Optional, Union
from uuid import UUID

async def get_captures(
    frame_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
) -> List[Capture]:
    """Get captures for a frame with pagination."""
    
# ✅ Good: Return type annotations
def calculate_confidence(
    detection_results: List[DetectionResult]
) -> float:
    if not detection_results:
        return 0.0
    return sum(r.confidence for r in detection_results) / len(detection_results)

# ✅ Good: Optional and Union types
def process_image(
    image_data: bytes,
    format: Optional[str] = None
) -> Union[bytes, None]:
```

### **FastAPI Standards**

```python
# ✅ Router organization
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List

router = APIRouter(prefix="/api/v1/captures", tags=["captures"])

@router.post("/", response_model=CaptureResponse, status_code=status.HTTP_201_CREATED)
async def create_capture(
    capture_data: CaptureCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> CaptureResponse:
    """Create a new capture."""
    try:
        capture = await capture_service.create_capture(
            db=db,
            capture_data=capture_data,
            user_id=current_user.id
        )
        return CaptureResponse.from_orm(capture)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create capture: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
```

### **Pydantic Schemas**

```python
# ✅ Schema organization
from pydantic import BaseModel, Field, validator
from typing import List, Optional
from uuid import UUID
from datetime import datetime

class CaptureBase(BaseModel):
    """Base capture schema."""
    frame_id: UUID = Field(..., description="ID of the parent frame")
    detection_results: List[DetectionResult] = Field(default=[], description="AI detection results")

class CaptureCreate(CaptureBase):
    """Schema for creating captures."""
    original_image_data: bytes = Field(..., description="Original image data")
    processed_image_data: bytes = Field(..., description="Processed image with detections")
    
    @validator('original_image_data')
    def validate_image_size(cls, v):
        if len(v) > MAX_FILE_SIZE:
            raise ValueError(f"Image size exceeds {MAX_FILE_SIZE} bytes")
        return v

class CaptureResponse(CaptureBase):
    """Schema for capture responses."""
    id: UUID
    created_at: datetime
    sync_status: SyncStatus
    
    class Config:
        from_attributes = True
```

### **Database Standards**

```python
# ✅ SQLAlchemy models
from sqlalchemy import Column, String, DateTime, Integer, LargeBinary, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

class Capture(Base):
    __tablename__ = "captures"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign keys
    frame_id = Column(UUID(as_uuid=True), ForeignKey("frames.id"), nullable=False)
    
    # Data fields
    original_image_data = Column(LargeBinary, nullable=False)
    processed_image_data = Column(LargeBinary, nullable=False)
    detection_results = Column(JSON, nullable=False, default=list)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    sync_version = Column(Integer, default=1, nullable=False)
    sync_status = Column(String(20), default="pending", nullable=False)
    
    # Relationships
    frame = relationship("Frame", back_populates="captures")
    
    # Indexes
    __table_args__ = (
        Index('idx_captures_frame_id', 'frame_id'),
        Index('idx_captures_sync_status', 'sync_status'),
    )
```

### **Service Layer Standards**

```python
# ✅ Service class pattern
from typing import List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

class CaptureService:
    """Service for capture operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = CaptureRepository(db)
    
    async def create_capture(
        self,
        capture_data: CaptureCreate,
        user_id: UUID
    ) -> Capture:
        """Create a new capture with validation."""
        # Validate frame exists and user has access
        frame = await self.frame_service.get_frame(capture_data.frame_id, user_id)
        if not frame:
            raise ValueError("Frame not found or access denied")
        
        # Create capture
        capture = Capture(
            frame_id=capture_data.frame_id,
            original_image_data=capture_data.original_image_data,
            processed_image_data=capture_data.processed_image_data,
            detection_results=capture_data.detection_results,
        )
        
        # Save to database
        saved_capture = await self.repository.create(capture)
        
        # Update frame capture count
        await self.frame_service.increment_capture_count(frame.id)
        
        return saved_capture
```

## 🗄️ Database Standards

### **Schema Design**

```sql
-- ✅ Good: Proper naming and constraints
CREATE TABLE captures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    frame_id UUID NOT NULL REFERENCES frames(id) ON DELETE CASCADE,
    original_image_data BYTEA NOT NULL,
    processed_image_data BYTEA NOT NULL,
    detection_results JSONB NOT NULL DEFAULT '[]'::jsonb,
    sync_version INTEGER NOT NULL DEFAULT 1,
    sync_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_captures_frame_id ON captures(frame_id);
CREATE INDEX idx_captures_sync_status ON captures(sync_status);
CREATE INDEX idx_captures_created_at ON captures(created_at DESC);
```

### **Migration Standards**

```python
# ✅ Alembic migration template
"""Add capture table

Revision ID: 001_add_captures
Revises: 
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '001_add_captures'
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create table
    op.create_table(
        'captures',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('frame_id', postgresql.UUID(as_uuid=True), nullable=False),
        # ... other columns
    )
    
    # Create indexes
    op.create_index('idx_captures_frame_id', 'captures', ['frame_id'])

def downgrade() -> None:
    op.drop_table('captures')
```

## 🧪 Testing Standards

### **Test Organization**

```
tests/
├── unit/               # Unit tests
│   ├── test_services/
│   ├── test_models/
│   └── test_utils/
├── integration/        # Integration tests
│   ├── test_api/
│   └── test_database/
├── e2e/               # End-to-end tests
└── fixtures/          # Test data and fixtures
```

### **Test Naming**

```python
# ✅ Descriptive test names
class TestCaptureService:
    async def test_create_capture_with_valid_data_succeeds(self):
        """Test that creating a capture with valid data succeeds."""
        
    async def test_create_capture_with_invalid_frame_id_raises_error(self):
        """Test that creating a capture with invalid frame ID raises error."""
        
    async def test_create_capture_updates_frame_capture_count(self):
        """Test that creating a capture increments the frame capture count."""
```

### **Test Structure**

```python
# ✅ AAA pattern (Arrange, Act, Assert)
async def test_create_capture_with_valid_data_succeeds(self):
    # Arrange
    frame = await create_test_frame()
    capture_data = CaptureCreate(
        frame_id=frame.id,
        original_image_data=b"test_image_data",
        processed_image_data=b"processed_image_data",
        detection_results=[]
    )
    
    # Act
    result = await capture_service.create_capture(capture_data, user.id)
    
    # Assert
    assert result.id is not None
    assert result.frame_id == frame.id
    assert result.sync_status == "pending"
```

## 📏 Code Quality Tools

### **Linting Configuration**

```json
// .eslintrc.json (Frontend)
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

```toml
# pyproject.toml (Backend)
[tool.ruff]
line-length = 88
target-version = "py39"
select = ["E", "F", "I", "N", "W", "UP"]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.black]
line-length = 88
target-version = ['py39']
```

### **Pre-commit Hooks**

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-json
      - id: check-yaml
      
  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black
        
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.254
    hooks:
      - id: ruff
```

## 📝 Documentation Standards

### **Code Comments**

```typescript
// ✅ Good: Explain why, not what
// Cache detection results to avoid expensive recomputation
// when the same image is processed multiple times
const detectionCache = new Map<string, DetectionResult[]>();

/**
 * Processes an image through the YOLOv8 model for object detection.
 * 
 * @param imageData - The image data as a Blob
 * @param model - The loaded TensorFlow.js model
 * @returns Promise resolving to detection results with bounding boxes
 * @throws {Error} When image preprocessing fails
 */
async function detectObjects(
  imageData: Blob, 
  model: tf.GraphModel
): Promise<DetectionResult[]> {
```

```python
# ✅ Good: Docstring standards
async def create_capture(
    self,
    capture_data: CaptureCreate,
    user_id: UUID
) -> Capture:
    """Create a new capture with validation and side effects.
    
    This method creates a capture, validates user permissions,
    and updates the parent frame's capture count atomically.
    
    Args:
        capture_data: The capture data to create
        user_id: ID of the user creating the capture
        
    Returns:
        The created capture instance
        
    Raises:
        ValueError: If the frame doesn't exist or user lacks access
        DatabaseError: If the database operation fails
    """
```

### **API Documentation**

```python
# ✅ FastAPI automatic documentation
@router.post(
    "/",
    response_model=CaptureResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create new capture",
    description="Create a new capture with image data and detection results",
    responses={
        201: {"description": "Capture created successfully"},
        400: {"description": "Invalid input data"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
    }
)
async def create_capture(
    capture_data: CaptureCreate = Body(..., description="Capture data to create"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> CaptureResponse:
```

---

These coding standards ensure consistency, maintainability, and quality across the entire codebase. All contributors should follow these guidelines to maintain the high quality of the weld defect detection system.