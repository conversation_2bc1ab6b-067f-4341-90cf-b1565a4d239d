# Testing Guide

Comprehensive testing procedures and guidelines for the Weld Defect Detection system.

## Testing Overview

The project uses a combination of manual testing scripts and integration testing approaches since no automated testing framework is currently configured.

```
┌─────────────────────────────────────────────────────────────┐
│                      Testing Strategy                       │
├─────────────────────────────────────────────────────────────┤
│  Backend Testing                Frontend Testing            │
│  ├─ Manual API Testing          ├─ Manual UI Testing       │
│  ├─ Integration Scripts         ├─ Browser DevTools        │
│  ├─ Database Testing            ├─ Network Monitoring      │
│  └─ Authentication Testing      └─ Performance Testing     │
├─────────────────────────────────────────────────────────────┤
│                    End-to-End Testing                      │
│  ├─ Full User Workflows                                    │
│  ├─ Sync Functionality                                     │
│  ├─ Offline/Online Transitions                             │
│  └─ Camera and AI Detection                                │
└─────────────────────────────────────────────────────────────┘
```

## Backend Testing

### Prerequisites for Backend Testing

1. **Start the backend server** (required for all tests):
```bash
cd backend
uv run uvicorn app.main:app --reload
```

2. **Verify server is running**:
```bash
curl http://localhost:8000/
# Should return: {"message": "Weld Defect Detection API - Backend Running"}
```

### Primary Testing Scripts

#### 1. Comprehensive API Testing (`test_api.py`)

This script tests the complete API workflow including authentication, CRUD operations, and error handling.

```bash
cd backend
uv run python test_api.py
```

**What it tests:**
- ✅ Server connectivity and health
- ✅ Authentication system (login/logout)
- ✅ JWT token generation and validation
- ✅ Protected endpoint access
- ✅ Frame creation and retrieval
- ✅ Error handling for unauthorized requests

**Expected output:**
```
🔧 Testing Weld Detection API Endpoints

✅ Server is running: Weld Defect Detection API - Backend Running
✅ Authentication is working (401 for unauth request)
✅ Login successful, got access token
✅ Frames endpoint accessible: X frames found
✅ Frame created successfully: frame-uuid-here
✅ Frame retrieved: WELD-001

🎉 API testing completed!
```

#### 2. Sync Functionality Testing (`test_sync.py`)

This script tests the synchronization endpoints and authentication requirements.

```bash
cd backend
uv run python test_sync.py
```

**What it tests:**
- ✅ API health endpoint
- ✅ Sync health endpoint
- ✅ Frame sync authentication requirements
- ✅ Proper error responses for unauthorized requests

**Expected output:**
```
🧪 Testing Sync Implementation...
==================================================
✅ API health endpoint working: {'status': 'healthy', ...}
✅ Sync health endpoint working: {'status': 'healthy', ...}
✅ Frame sync correctly requires authentication

📊 Test Results:
==================================================
API Health: ✅ PASS
Sync Health: ✅ PASS
Frame Sync Auth: ✅ PASS

🎯 Overall: ✅ ALL TESTS PASSED

✨ Sync implementation is working correctly!
```

### Manual API Testing

#### Authentication Testing
```bash
# Test login endpoint
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=inspector1&password=password123"

# Expected response:
# {
#   "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
#   "token_type": "bearer"
# }
```

#### Protected Endpoints
```bash
# Get access token first
TOKEN=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=inspector1&password=password123" | jq -r '.access_token')

# Test protected endpoint
curl -X GET http://localhost:8000/api/v1/frames/ \
  -H "Authorization: Bearer $TOKEN"
```

#### Frame Operations
```bash
# Create frame
curl -X POST http://localhost:8000/api/v1/frames/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model_number": "TEST-001",
    "machine_serial_number": "MACHINE-123",
    "inspector_name": "Test Inspector",
    "status": "active",
    "metadata": {"test": true}
  }'

# Get frame by ID
curl -X GET http://localhost:8000/api/v1/frames/{frame_id} \
  -H "Authorization: Bearer $TOKEN"

# List all frames
curl -X GET http://localhost:8000/api/v1/frames/?limit=10&offset=0 \
  -H "Authorization: Bearer $TOKEN"
```

#### Sync Operations
```bash
# Test sync health
curl -X GET http://localhost:8000/api/v1/sync/health

# Test frame sync (requires authentication)
curl -X POST http://localhost:8000/api/v1/sync/frame \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "operation_type": "create",
    "object_type": "frame",
    "object_id": "test-frame-123",
    "frame_data": {
      "frameId": "test-frame-123",
      "modelNumber": "TEST-001",
      "machineSerialNumber": "MACHINE-001",
      "inspectorName": "Test Inspector"
    }
  }'
```

### Database Testing

#### Inspect Database State
```bash
cd backend

# Check database tables
uv run python -c "
from app.database import engine
from sqlalchemy import inspect, text
import asyncio

async def check_db():
    async with engine.begin() as conn:
        inspector = inspect(conn)
        tables = await conn.run_sync(inspector.get_table_names)
        print('Tables:', tables)
        
        # Check users table
        result = await conn.execute(text('SELECT username, email FROM users'))
        users = result.fetchall()
        print('Users:', users)

asyncio.run(check_db())
"
```

#### Reset Database for Testing
```bash
cd backend

# Backup current database
cp weld_detection.db weld_detection.db.backup

# Remove database (will be recreated on next startup)
rm -f weld_detection.db

# Restart server to recreate database
uv run uvicorn app.main:app --reload
```

### Performance Testing

#### Load Testing with curl
```bash
# Test multiple concurrent requests
for i in {1..10}; do
  curl -s http://localhost:8000/api/health &
done
wait

# Time API response
time curl -s http://localhost:8000/api/health
```

#### Database Performance
```bash
cd backend

# Check database query performance
uv run python -c "
import time
from app.database import get_db
from app.models.database import Frame
from sqlalchemy import select

async def test_query_performance():
    async for db in get_db():
        start = time.time()
        result = await db.execute(select(Frame).limit(100))
        frames = result.scalars().all()
        end = time.time()
        print(f'Query time: {end - start:.3f}s, Results: {len(frames)}')
        break

import asyncio
asyncio.run(test_query_performance())
"
```

## Frontend Testing

### Prerequisites for Frontend Testing

1. **Start both servers**:
```bash
# Terminal 1 - Backend
cd backend && uv run uvicorn app.main:app --reload

# Terminal 2 - Frontend
cd frontend && npm run dev
```

2. **Open browser** to http://localhost:3000

### Code Quality Testing

#### ESLint Testing
```bash
cd frontend

# Run linting
npm run lint

# Fix auto-fixable issues
npm run lint -- --fix

# Check specific files
npx eslint src/components/detection/CameraPanel.tsx
```

#### TypeScript Compilation
```bash
cd frontend

# Type checking without emitting files
npx tsc --noEmit

# Watch mode for continuous checking
npx tsc --noEmit --watch

# Check specific files
npx tsc --noEmit src/lib/db/types.ts
```

### Manual UI Testing

#### Authentication Flow
1. **Navigate to login page**: http://localhost:3000/login
2. **Test valid credentials**:
   - Username: `inspector1`
   - Password: `password123`
   - Expected: Redirect to home page
3. **Test invalid credentials**:
   - Username: `invalid`
   - Password: `invalid`
   - Expected: Error message displayed
4. **Test logout**:
   - Click logout button
   - Expected: Redirect to login page

#### Session Management
1. **Create new session**:
   - Model Number: `WELD-001`
   - Machine Serial: `MACHINE-123`
   - Inspector Name: `Test Inspector`
   - Expected: Navigate to detection page
2. **Edit session**:
   - Click edit button on session card
   - Modify fields
   - Save changes
   - Expected: Changes reflected immediately
3. **Session persistence**:
   - Refresh page
   - Expected: Session data preserved

#### Camera and Detection Testing
1. **Camera access**:
   - Navigate to detection page
   - Allow camera permission
   - Expected: Live camera feed displayed
2. **Image capture**:
   - Click capture button
   - Expected: Image captured and processed
3. **Detection results**:
   - Expected: Bounding boxes and labels displayed
   - Expected: Results added to history panel
4. **Offline functionality**:
   - Disconnect internet
   - Capture images
   - Expected: Data stored locally
   - Reconnect internet
   - Expected: Data syncs to server

### Browser DevTools Testing

#### Network Monitoring
1. **Open DevTools** → Network tab
2. **Monitor API calls**:
   - Login requests to `/api/v1/auth/login`
   - Frame requests to `/api/v1/frames/`
   - Sync requests to `/api/v1/sync/`
3. **Check response times**:
   - API calls should complete within 500ms
   - Large file uploads may take longer

#### IndexedDB Inspection
1. **Open DevTools** → Application tab
2. **Navigate to IndexedDB** → WeldDetectionDB
3. **Inspect tables**:
   - `frames`: Session data
   - `captures`: Image data and detection results
   - `syncQueue`: Pending sync operations
4. **Verify data integrity**:
   - Check that data is properly structured
   - Verify foreign key relationships

#### Console Monitoring
1. **Open DevTools** → Console tab
2. **Monitor for errors**:
   - No red error messages during normal operation
   - Warnings are acceptable for development
3. **Check sync status**:
   - Sync operations should complete successfully
   - Look for sync-related log messages

### Performance Testing

#### Page Load Performance
```bash
cd frontend

# Build production version
npm run build

# Start production server
npm run start

# Test with browser DevTools:
# 1. Open Lighthouse tab
# 2. Run audit
# 3. Check Performance score (target: >80)
```

#### Memory Usage
1. **Open DevTools** → Memory tab
2. **Take heap snapshots**:
   - Before creating sessions
   - After creating multiple sessions
   - After capturing images
3. **Monitor for memory leaks**:
   - Memory usage should stabilize
   - No continuous growth over time

## End-to-End Testing

### Complete User Workflow

#### 1. Authentication and Setup
```bash
# Test sequence:
1. Open http://localhost:3000
2. Login with inspector1/password123
3. Verify redirect to home page
4. Check connection status indicator
```

#### 2. Session Creation and Management
```bash
# Test sequence:
1. Click "Create New Session"
2. Fill form with test data
3. Submit and verify navigation
4. Check session appears in history
5. Edit session details
6. Verify changes persist
```

#### 3. Detection Workflow
```bash
# Test sequence:
1. Navigate to detection page
2. Allow camera access
3. Capture multiple images
4. Verify detection results
5. Check history panel updates
6. Verify data in IndexedDB
```

#### 4. Sync Testing
```bash
# Test sequence:
1. Create data while online
2. Verify sync to server
3. Disconnect internet
4. Create more data
5. Verify offline storage
6. Reconnect internet
7. Verify automatic sync
8. Check server data matches client
```

### Offline/Online Transitions

#### Offline Mode Testing
1. **Go offline**:
   - Disconnect network
   - Or use DevTools → Network → Offline
2. **Create data**:
   - Create sessions
   - Capture images
   - Verify functionality continues
3. **Check sync queue**:
   - Verify operations queued for sync
   - No data loss during offline period

#### Online Recovery Testing
1. **Reconnect network**
2. **Monitor sync process**:
   - Automatic sync should start
   - Progress indicators should appear
   - Conflicts should be resolved
3. **Verify data integrity**:
   - All offline data synced
   - No duplicate records
   - Timestamps preserved

### Camera and AI Testing

#### Camera Hardware Testing
1. **Different cameras**:
   - Built-in webcam
   - External USB camera
   - Mobile device camera
2. **Different resolutions**:
   - Test various camera resolutions
   - Verify detection accuracy
3. **Lighting conditions**:
   - Good lighting
   - Poor lighting
   - Verify detection robustness

#### AI Model Testing
1. **Model loading**:
   - Verify model loads successfully
   - Check loading time (should be <3 seconds)
2. **Detection accuracy**:
   - Test with various objects
   - Verify bounding box accuracy
   - Check confidence scores
3. **Performance**:
   - Measure inference time
   - Monitor memory usage
   - Test on different devices

## Troubleshooting Tests

### Common Test Failures

#### Backend Tests Fail
```bash
# Check server is running
curl http://localhost:8000/
# If fails, start server:
cd backend && uv run uvicorn app.main:app --reload

# Check authentication
curl -X POST http://localhost:8000/api/v1/auth/login \
  -d "username=inspector1&password=password123"
# If fails, reset database:
rm -f weld_detection.db && restart server
```

#### Frontend Tests Fail
```bash
# Check frontend server
curl http://localhost:3000/
# If fails, start server:
cd frontend && npm run dev

# Check TypeScript compilation
npx tsc --noEmit
# If fails, fix TypeScript errors

# Check linting
npm run lint
# If fails, fix linting errors
```

#### Camera Tests Fail
- **Check browser permissions**: Allow camera access
- **Try different browser**: Chrome recommended
- **Check HTTPS**: Camera requires secure context
- **Test different camera**: Try external USB camera

#### Sync Tests Fail
- **Check network connectivity**: Verify internet connection
- **Check server status**: Ensure backend is running
- **Clear IndexedDB**: Reset client-side database
- **Check authentication**: Verify login token is valid

### Test Data Cleanup

#### Reset Client Data
```javascript
// In browser console
indexedDB.deleteDatabase('WeldDetectionDB');
location.reload();
```

#### Reset Server Data
```bash
cd backend
rm -f weld_detection.db
# Restart server to recreate database
```

#### Reset Both
```bash
# Reset server
cd backend && rm -f weld_detection.db

# Reset client (in browser console)
indexedDB.deleteDatabase('WeldDetectionDB');
location.reload();

# Restart both servers
```

## Continuous Testing

### Pre-commit Testing
```bash
# Run before each commit
cd backend && uv run python test_api.py
cd frontend && npm run lint && npx tsc --noEmit
```

### Daily Testing Routine
```bash
# Morning routine
1. Start both servers
2. Run comprehensive API tests
3. Test sync functionality
4. Create test session and captures
5. Verify offline/online transitions
```

### Release Testing
```bash
# Before each release
1. Run all manual tests
2. Test on multiple browsers
3. Test on different devices
4. Performance testing
5. Security testing
6. Backup and restore testing
```

## Next Steps

For automated testing implementation:

1. **Backend**: Add pytest with test fixtures and database mocking
2. **Frontend**: Add Jest and React Testing Library
3. **E2E**: Add Playwright or Cypress for automated UI testing
4. **CI/CD**: Add GitHub Actions for automated testing

For now, follow this manual testing guide to ensure system reliability and functionality.