# Database Schema Architecture

## Overview

The Weld Defect Detection System employs a dual-database architecture with IndexedDB on the client side and SQLite on the server side. Both databases maintain synchronized schemas to ensure seamless data synchronization while supporting offline-first operations.

## Schema Design Principles

### 1. Offline-First Design
- Client schema mirrors server schema for seamless sync
- Sync status tracking on every record
- Conflict resolution through versioning

### 2. Performance Optimization
- Strategic indexing for common query patterns
- Denormalized fields for faster lookups
- Batch operation support

### 3. Data Integrity
- Foreign key relationships maintained
- Cascade delete for related records
- Validation at both client and server levels

## Client-Side Schema (IndexedDB)

### Database Structure
```typescript
interface WeldDetectionDB extends DBSchema {
  frames: {
    key: string;              // frameId (primary key)
    value: Frame;
    indexes: {
      'by-machine-inspector': [string, string];  // Composite index
      'by-sync-status': string;                  // Sync operations
    };
  };
  captures: {
    key: string;              // captureId (primary key)
    value: Capture;
    indexes: {
      'by-frame': string;                        // Foreign key index
      'by-sync-status': string;                  // Sync operations
    };
  };
  syncQueue: {
    key: number;              // Auto-increment queueId
    value: SyncQueueItem;
    indexes: {
      'by-status-priority': [string, number];    // Queue processing
      'by-object': [string, string];             // Object lookup
    };
  };
}
```

### Frame Entity (Session)
```typescript
interface Frame {
  frameId: string;                    // UUID primary key
  modelNumber: string;                // Equipment model
  machineSerialNumber: string;        // Machine identifier
  inspectorName: string;              // Inspector performing detection
  creationTimestamp: number;          // Unix timestamp (ms)
  lastModifiedTimestamp: number;      // Unix timestamp (ms)
  status: 'active' | 'completed' | 'archived';
  captureCount: number;               // Denormalized count
  metadata?: Record<string, unknown>; // Extensible metadata
  syncStatus: 'synced' | 'pending' | 'conflict';
  lastSyncedAt?: number;              // Unix timestamp (ms)
}
```

### Capture Entity (Detection Result)
```typescript
interface Capture {
  captureId: string;                  // UUID primary key
  frameId: string;                    // Foreign key to Frame
  captureTimestamp: number;           // Unix timestamp (ms)
  originalImageBlob?: Blob;           // Original camera image
  processedImageBlob?: Blob;          // Image with detection overlays
  thumbnailBlob?: Blob;               // Thumbnail for UI
  detectionResults: DetectionResult[]; // AI detection results
  syncStatus: 'synced' | 'pending' | 'conflict';
  syncVersion: number;                // Optimistic concurrency control
  lastSyncAttempt?: number;           // Unix timestamp (ms)
}
```

### Detection Result (AI Output)
```typescript
interface DetectionResult {
  id: string;                         // UUID for detection
  class: string;                      // Detected object class
  confidence: number;                 // Detection confidence (0-1)
  bbox: {                             // Bounding box coordinates
    x1: number;                       // Top-left X
    y1: number;                       // Top-left Y
    x2: number;                       // Bottom-right X
    y2: number;                       // Bottom-right Y
  };
}
```

### Sync Queue Entity
```typescript
interface SyncQueueItem {
  queueId?: number;                   // Auto-increment primary key
  operationType: 'create' | 'update' | 'delete';
  objectType: 'frame' | 'capture';
  objectId: string;                   // Reference to Frame/Capture
  priority: number;                   // Processing priority (1-10)
  createdAt: number;                  // Unix timestamp (ms)
  attemptCount: number;               // Retry tracking
  lastAttempt?: number;               // Unix timestamp (ms)
  status: 'pending' | 'processing' | 'completed' | 'failed';
}
```

## Server-Side Schema (SQLite)

### Database Models (SQLAlchemy)

#### User Management
```python
class User(Base):
    __tablename__ = "users"
    
    user_id = Column(String, primary_key=True, default=uuid4)
    username = Column(String, unique=True, nullable=False, index=True)
    email = Column(String, unique=True, nullable=False, index=True)
    full_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    role = Column(Enum(UserRole), default=UserRole.INSPECTOR)
    is_active = Column(Boolean, default=True)
    created_at = Column(Integer, nullable=False)
    last_login = Column(Integer, nullable=True)
    
    # Relationships
    frames = relationship("Frame", back_populates="inspector")
```

#### Frame (Session) Management
```python
class Frame(Base):
    __tablename__ = "frames"
    
    frame_id = Column(String, primary_key=True, default=uuid4)
    model_number = Column(String, nullable=False)
    machine_serial_number = Column(String, nullable=False)
    inspector_id = Column(String, ForeignKey("users.user_id"), nullable=False)
    inspector_name = Column(String, nullable=False)  # Backward compatibility
    creation_timestamp = Column(Integer, nullable=False)
    last_modified_timestamp = Column(Integer, nullable=False)
    status = Column(Enum(FrameStatus), default=FrameStatus.ACTIVE)
    capture_count = Column(Integer, default=0)
    frame_metadata = Column(JSON, nullable=True)
    sync_status = Column(Enum(SyncStatus), default=SyncStatus.SYNCED)
    last_synced_at = Column(Integer, nullable=True)
    
    # Relationships
    inspector = relationship("User", back_populates="frames")
    captures = relationship("Capture", back_populates="frame", 
                          cascade="all, delete-orphan")
```

#### Capture (Detection) Management
```python
class Capture(Base):
    __tablename__ = "captures"
    
    capture_id = Column(String, primary_key=True, default=uuid4)
    frame_id = Column(String, ForeignKey("frames.frame_id"), nullable=False)
    capture_timestamp = Column(Integer, nullable=False)
    original_image_blob = Column(LargeBinary, nullable=True)
    processed_image_blob = Column(LargeBinary, nullable=True)
    thumbnail_blob = Column(LargeBinary, nullable=True)
    detection_results = Column(JSON, nullable=False, default=list)
    sync_status = Column(Enum(SyncStatus), default=SyncStatus.SYNCED)
    sync_version = Column(Integer, default=1)
    last_sync_attempt = Column(Integer, nullable=True)
    
    # Relationship
    frame = relationship("Frame", back_populates="captures")
```

#### Sync Queue Management
```python
class SyncQueueItem(Base):
    __tablename__ = "sync_queue"
    
    queue_id = Column(Integer, primary_key=True, autoincrement=True)
    operation_type = Column(Enum(OperationType), nullable=False)
    object_type = Column(Enum(ObjectType), nullable=False)
    object_id = Column(String, nullable=False)
    priority = Column(Integer, default=1)
    created_at = Column(Integer, nullable=False)
    attempt_count = Column(Integer, default=0)
    last_attempt = Column(Integer, nullable=True)
    status = Column(Enum(QueueStatus), default=QueueStatus.PENDING)
```

## Database Indexes and Performance

### Client-Side Indexes (IndexedDB)
```typescript
// Frame indexes
framesStore.createIndex('by-machine-inspector', 
  ['machineSerialNumber', 'inspectorName']);
framesStore.createIndex('by-sync-status', 'syncStatus');

// Capture indexes
capturesStore.createIndex('by-frame', 'frameId');
capturesStore.createIndex('by-sync-status', 'syncStatus');

// Sync queue indexes
syncQueueStore.createIndex('by-status-priority', ['status', 'priority']);
syncQueueStore.createIndex('by-object', ['objectId', 'objectType']);
```

### Server-Side Indexes (SQLite)
```python
# Capture table performance indexes
__table_args__ = (
    # Most common query: get captures by frame ordered by timestamp
    Index('idx_captures_frame_timestamp', 'frame_id', 'capture_timestamp'),
    
    # Sync operations: find captures by sync status
    Index('idx_captures_sync_status', 'sync_status'),
    
    # Conflict resolution: lookup by capture_id and sync_version
    Index('idx_captures_sync_version', 'capture_id', 'sync_version'),
    
    # Frame-based sync operations
    Index('idx_captures_frame_sync', 'frame_id', 'sync_status'),
    
    # Timestamp-based queries
    Index('idx_captures_timestamp', 'capture_timestamp'),
)
```

## Data Relationships

### Entity Relationship Diagram
```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│      User       │       │      Frame      │       │    Capture      │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ user_id (PK)    │◄──────┤ inspector_id(FK)│◄──────┤ frame_id (FK)   │
│ username        │       │ frame_id (PK)   │       │ capture_id (PK) │
│ email           │       │ model_number    │       │ capture_timestamp│
│ full_name       │       │ machine_serial  │       │ detection_results│
│ role            │       │ inspector_name  │       │ image_blobs     │
│ is_active       │       │ timestamps      │       │ sync_status     │
└─────────────────┘       │ sync_status     │       └─────────────────┘
                          └─────────────────┘
                                   │
                                   ▼
                          ┌─────────────────┐
                          │   SyncQueue     │
                          ├─────────────────┤
                          │ queue_id (PK)   │
                          │ object_id (FK)  │
                          │ operation_type  │
                          │ object_type     │
                          │ status          │
                          │ attempt_count   │
                          └─────────────────┘
```

### Relationship Rules
1. **User → Frame**: One-to-Many (Inspector can have multiple sessions)
2. **Frame → Capture**: One-to-Many (Session can have multiple detections)
3. **SyncQueue → Frame/Capture**: References any object for sync operations
4. **Cascade Deletes**: Removing a Frame deletes all its Captures
5. **Soft Deletes**: Users marked inactive rather than deleted

## Data Migration Strategy

### Database Versioning
```typescript
// Client-side migration example
const DB_VERSION = 2;

upgrade(db, oldVersion, newVersion, transaction) {
  if (oldVersion < 1) {
    // Initial schema creation
    createInitialSchema(db);
  }
  
  if (oldVersion < 2) {
    // Add new indexes
    const syncQueueStore = transaction.objectStore('syncQueue');
    syncQueueStore.createIndex('by-object', ['objectId', 'objectType']);
  }
}
```

### Server-Side Migrations (Alembic)
```python
# Migration example: 002_add_inspector_id_column.py
def upgrade():
    op.add_column('frames', sa.Column('inspector_id', sa.String(), nullable=True))
    op.create_foreign_key('fk_frames_inspector', 'frames', 'users', 
                         ['inspector_id'], ['user_id'])
    
    # Backfill existing data
    # ... migration logic ...

def downgrade():
    op.drop_constraint('fk_frames_inspector', 'frames')
    op.drop_column('frames', 'inspector_id')
```

## Sync Strategy Implementation

### Conflict Resolution
1. **Last-Write-Wins**: Simple timestamp-based resolution
2. **Version Control**: Optimistic concurrency with sync_version
3. **Manual Resolution**: Flag conflicts for user review
4. **Field-Level Merging**: Merge non-conflicting field changes

### Sync Status States
- **synced**: Data matches server state
- **pending**: Local changes waiting for sync
- **conflict**: Server has newer version, needs resolution

### Performance Optimization
1. **Batch Operations**: Group multiple changes in single sync
2. **Differential Sync**: Only sync changed fields
3. **Compression**: Compress large image blobs
4. **Lazy Loading**: Load images on demand

## Data Integrity Measures

### Client-Side Validation
- TypeScript type safety
- Schema validation before IndexedDB operations
- Referential integrity checks

### Server-Side Validation
- Pydantic models for API validation
- SQLAlchemy constraints and relationships
- Custom business logic validation

### Backup and Recovery
- IndexedDB export/import functionality
- SQLite backup strategies
- Image file system backup procedures

This comprehensive database schema supports the offline-first architecture while ensuring data integrity, performance, and seamless synchronization between client and server systems.