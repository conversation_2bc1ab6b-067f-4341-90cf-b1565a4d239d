# System Architecture Overview

## Executive Summary

The Weld Defect Detection System is a production-ready, offline-first web application that provides real-time AI-powered weld defect detection using computer vision. The system is architected for industrial environments where network connectivity may be intermittent, ensuring continuous operation through client-side AI inference and intelligent data synchronization.

## Core Architecture

### Technology Stack

**Frontend (Next.js 15)**
- **Framework**: Next.js 15 with App Router and React 19
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS v4 with shadcn/ui components
- **AI/ML**: TensorFlow.js with YOLOv8n model for client-side inference
- **Storage**: IndexedDB for offline-first data persistence
- **Camera**: WebRTC for real-time camera integration

**Backend (FastAPI)**
- **Framework**: FastAPI with async/await support
- **Language**: Python with comprehensive type hints
- **Database**: SQLite with SQLAlchemy ORM and async support
- **Authentication**: JWT-based auth with role-based access control
- **File Storage**: Local filesystem with image processing capabilities
- **Migrations**: Alembic for database schema management

### High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client (<PERSON>rowser)                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Next.js UI    │  │ TensorFlow.js   │  │   IndexedDB     │  │
│  │   Components    │  │  YOLOv8n Model  │  │  Offline Store  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│           │                     │                     │         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Session Mgmt   │  │ Camera/WebRTC   │  │  Sync Manager   │  │
│  │   Context       │  │   Integration   │  │   Background    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                        ┌───────┴───────┐
                        │  HTTP/REST    │
                        │   API Calls   │
                        └───────┬───────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      Server (FastAPI)                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Auth Service   │  │  Sync Service   │  │  File Storage   │  │
│  │  JWT + Roles    │  │  Conflict Res   │  │   Service       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│           │                     │                     │         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   User Mgmt     │  │  Frame/Capture  │  │   Database      │  │
│  │   Repository    │  │    Services     │  │   SQLite        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Application Flow

### 1. Session Management
```
Home Page → Session Form → Detection Interface
    ↓
User Authentication (JWT)
    ↓
Session Context (Global State)
    ↓
IndexedDB Persistence
```

### 2. Detection Workflow
```
Camera Capture → TensorFlow.js Inference → Detection Results
    ↓
IndexedDB Storage (Offline-First)
    ↓
Sync Queue Management
    ↓
Background Sync to Server (When Connected)
```

### 3. Data Synchronization
```
Client Operations → IndexedDB → Sync Queue
    ↓
Background Sync Process
    ↓
Server API Endpoints
    ↓
SQLite Database
    ↓
Conflict Resolution & Status Updates
```

## Key Design Principles

### 1. Offline-First Architecture
- **Primary Storage**: All data operations go through IndexedDB first
- **Sync Queue**: Operations are queued for background synchronization
- **Graceful Degradation**: Full functionality available without network
- **Optimistic UI**: Immediate feedback with rollback on sync failure

### 2. Real-Time AI Inference
- **Client-Side Processing**: TensorFlow.js runs YOLOv8n model in browser
- **WebRTC Integration**: Direct camera access for real-time detection
- **Performance Optimization**: Efficient tensor management and cleanup
- **Configurable Thresholds**: Adjustable confidence and IoU thresholds

### 3. Production-Ready Backend
- **Async Architecture**: FastAPI with SQLAlchemy async support
- **Comprehensive Auth**: JWT with role-based access control
- **Robust Sync**: Duplicate detection, conflict resolution, retry logic
- **Performance Indexes**: Database optimizations for common queries

### 4. Scalable Frontend
- **Component Architecture**: Modular, reusable UI components
- **State Management**: React Context with TypeScript
- **Event-Driven Sync**: Real-time UI updates via event emitters
- **Error Handling**: Comprehensive error boundaries and fallbacks

## Security Architecture

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication with refresh token support
- **Role-Based Access**: Admin and Inspector roles with different permissions
- **Secure Storage**: Tokens stored in localStorage with validation
- **API Protection**: All sync endpoints require authentication

### Data Security
- **Client-Side Encryption**: Sensitive data encrypted before IndexedDB storage
- **Server Validation**: Comprehensive input validation and sanitization
- **File Security**: Image uploads validated and processed server-side
- **Audit Trail**: All operations logged with user attribution

## Performance Characteristics

### Client-Side Performance
- **AI Inference**: ~50-200ms per frame depending on hardware
- **IndexedDB Operations**: Sub-millisecond for most queries
- **Memory Management**: Automatic tensor cleanup prevents memory leaks
- **Batch Operations**: Optimized for high-frequency capture scenarios

### Server Performance
- **Database Indexes**: Optimized queries for sync operations
- **Async Processing**: Non-blocking I/O for concurrent requests
- **File Compression**: Automatic image optimization and thumbnail generation
- **Connection Pooling**: Efficient database connection management

## Monitoring & Observability

### Client-Side Monitoring
- **Sync Status Tracking**: Real-time sync queue status
- **Performance Metrics**: Detection timing and success rates
- **Error Reporting**: Comprehensive error logging and user feedback
- **Storage Monitoring**: IndexedDB usage and cleanup

### Server-Side Monitoring
- **Health Checks**: Comprehensive service health endpoints
- **Sync Statistics**: Per-user sync success rates and timing
- **Database Monitoring**: Query performance and optimization metrics
- **File Storage Tracking**: Image storage usage and cleanup

## Deployment Architecture

### Development Environment
```bash
# Frontend
cd frontend && npm run dev    # Next.js with Turbopack

# Backend
cd backend && uv run uvicorn app.main:app --reload
```

### Production Considerations
- **Frontend**: Static site generation with ISR for optimal performance
- **Backend**: ASGI server (Uvicorn) with reverse proxy (nginx)
- **Database**: SQLite with WAL mode for better concurrency
- **File Storage**: Local filesystem with backup strategy
- **Monitoring**: Health checks and logging integration

## Integration Points

### External Systems
- **Camera Hardware**: WebRTC compatibility for industrial cameras
- **File Systems**: Configurable storage backends for images
- **Authentication**: Extensible for LDAP/AD integration
- **Analytics**: Export capabilities for business intelligence

### API Interfaces
- **REST API**: Comprehensive CRUD operations for all entities
- **Sync API**: Specialized endpoints for offline-first synchronization
- **Auth API**: JWT-based authentication and user management
- **Health API**: System monitoring and diagnostics

This architecture ensures the system is production-ready, scalable, and maintains full functionality in offline scenarios while providing seamless synchronization when connectivity is available.