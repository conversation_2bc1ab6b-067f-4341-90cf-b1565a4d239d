# Offline-First Design Patterns

## Overview

The Weld Defect Detection System is architected with offline-first principles at its core, ensuring full functionality without network connectivity while providing seamless synchronization when connections are available. This design pattern is critical for industrial environments where network reliability may be inconsistent.

## Core Offline-First Principles

### 1. Local-First Data Operations
- **Primary Storage**: IndexedDB serves as the single source of truth on the client
- **Immediate Responses**: All user operations complete instantly against local storage
- **Background Sync**: Server synchronization happens asynchronously without blocking UI
- **Graceful Degradation**: Full application functionality available offline

### 2. Progressive Enhancement
- **Base Functionality**: Complete weld detection workflow works offline
- **Enhanced Features**: Additional features activate when online (user management, reporting)
- **Status Awareness**: Clear indication of online/offline status and sync state
- **Conflict Resolution**: Intelligent handling of data conflicts when reconnecting

### 3. Resilient Architecture
- **Fault Tolerance**: System continues operation despite network failures
- **Data Integrity**: Consistent data state regardless of connection status
- **Recovery Mechanisms**: Automatic recovery from various failure scenarios
- **User Experience**: Seamless experience across online/offline transitions

## Offline-First Architecture Components

### Client-Side Storage Strategy

#### IndexedDB as Primary Database
```typescript
// Database initialization with offline-first design
interface WeldDetectionDB extends DBSchema {
  frames: {
    key: string;
    value: Frame;
    indexes: {
      'by-machine-inspector': [string, string];
      'by-sync-status': string;          // Critical for offline operations
    };
  };
  captures: {
    key: string;
    value: Capture;
    indexes: {
      'by-frame': string;
      'by-sync-status': string;          // Track sync state
    };
  };
  syncQueue: {
    key: number;
    value: SyncQueueItem;
    indexes: {
      'by-status-priority': [string, number];  // Offline queue management
    };
  };
}
```

#### Local-First CRUD Operations
```typescript
// All operations complete locally first
export async function createFrame(frameData: Omit<Frame, 'frameId'>): Promise<Frame> {
  const frame: Frame = {
    ...frameData,
    frameId: crypto.randomUUID(),
    creationTimestamp: Date.now(),
    lastModifiedTimestamp: Date.now(),
    syncStatus: 'pending',  // Marked for sync when online
    captureCount: 0
  };

  // 1. Store locally immediately
  await storeFrame(frame);
  
  // 2. Queue for background sync (non-blocking)
  await addToSyncQueue('create', 'frame', frame.frameId);
  
  // 3. Return immediately - user sees instant response
  return frame;
}
```

### Background Synchronization

#### Sync Queue Management
```typescript
interface SyncQueueItem {
  queueId?: number;
  operationType: 'create' | 'update' | 'delete';
  objectType: 'frame' | 'capture';
  objectId: string;
  priority: number;          // 1-10, higher numbers sync first
  createdAt: number;
  attemptCount: number;      // Retry tracking
  lastAttempt?: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

// Queue operations are non-blocking and prioritized
export async function addToSyncQueue(
  operation: OperationType,
  objectType: ObjectType,
  objectId: string,
  priority: number = 5
): Promise<void> {
  const queueItem: SyncQueueItem = {
    operationType: operation,
    objectType,
    objectId,
    priority,
    createdAt: Date.now(),
    attemptCount: 0,
    status: 'pending'
  };

  await storeSyncQueueItem(queueItem);
  
  // Trigger background sync if online
  if (navigator.onLine) {
    // Non-blocking background sync
    syncManager.processSyncQueue().catch(console.error);
  }
}
```

#### Network State Management
```typescript
export class ConnectionManager {
  private isOnline: boolean = navigator.onLine;
  private listeners: Array<(online: boolean) => void> = [];

  constructor() {
    // Monitor network status changes
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.notifyListeners(true);
      this.triggerBackgroundSync();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifyListeners(false);
    });
  }

  private async triggerBackgroundSync(): Promise<void> {
    // Automatically start sync when coming online
    try {
      await syncManager.processSyncQueue();
    } catch (error) {
      console.error('Background sync failed:', error);
    }
  }

  onStatusChange(callback: (online: boolean) => void): void {
    this.listeners.push(callback);
  }

  get online(): boolean {
    return this.isOnline;
  }
}
```

## AI Inference Offline Strategy

### Client-Side Model Loading
```typescript
export class ModelLoader {
  private static instance: ModelLoader;
  private model: tf.GraphModel | null = null;
  private isLoading: boolean = false;

  async loadModel(): Promise<tf.GraphModel> {
    if (this.model) return this.model;
    
    if (this.isLoading) {
      // Wait for existing load to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.model!;
    }

    this.isLoading = true;
    
    try {
      // Load model from local public directory (offline-accessible)
      this.model = await tf.loadGraphModel('/models/yolov8n_web_model/model.json');
      console.log('YOLOv8n model loaded successfully (offline-ready)');
      return this.model;
    } catch (error) {
      console.error('Failed to load model:', error);
      throw new Error('AI model loading failed - offline detection unavailable');
    } finally {
      this.isLoading = false;
    }
  }

  // Cleanup for memory management
  dispose(): void {
    if (this.model) {
      this.model.dispose();
      this.model = null;
    }
  }
}
```

### Offline Detection Pipeline
```typescript
export async function performOfflineDetection(
  imageSource: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement,
  options: DetectionOptions = {}
): Promise<{ detections: DetectionResult[]; metrics: PerformanceMetrics }> {
  // Ensure model is loaded (cached locally)
  const model = await ModelLoader.getInstance().loadModel();
  
  // Perform detection entirely client-side
  const result = await detect(imageSource, model, options);
  
  // Store results locally immediately
  const capture: Capture = {
    captureId: crypto.randomUUID(),
    frameId: getCurrentFrameId(),
    captureTimestamp: Date.now(),
    detectionResults: result.detections,
    syncStatus: 'pending',
    syncVersion: 1
  };

  // Process images offline
  const { originalBlob, processedBlob, thumbnailBlob } = await processImages(
    imageSource, 
    result.detections
  );
  
  capture.originalImageBlob = originalBlob;
  capture.processedImageBlob = processedBlob;
  capture.thumbnailBlob = thumbnailBlob;

  // Store locally (immediate response)
  await storeCapture(capture);
  
  // Queue for sync when online
  await addToSyncQueue('create', 'capture', capture.captureId, 8); // High priority
  
  return result;
}
```

## Data Persistence Strategies

### Optimistic Updates
```typescript
export async function updateFrame(
  frameId: string, 
  updates: Partial<Frame>
): Promise<Frame> {
  // Get current frame
  const currentFrame = await getFrameById(frameId);
  if (!currentFrame) throw new Error('Frame not found');

  // Apply updates optimistically
  const updatedFrame: Frame = {
    ...currentFrame,
    ...updates,
    lastModifiedTimestamp: Date.now(),
    syncStatus: 'pending'  // Mark for sync
  };

  // Update locally immediately (optimistic)
  await storeFrame(updatedFrame);
  
  // Queue for background sync
  await addToSyncQueue('update', 'frame', frameId);
  
  // UI updates immediately with optimistic data
  notifyFrameUpdated(updatedFrame);
  
  return updatedFrame;
}
```

### Conflict Resolution on Reconnection
```typescript
export async function resolveConflicts(): Promise<ConflictResolution[]> {
  const conflictedItems = await getItemsByStatus('conflict');
  const resolutions: ConflictResolution[] = [];

  for (const item of conflictedItems) {
    try {
      // Fetch latest server version
      const serverVersion = await fetchServerVersion(item.id);
      
      // Apply resolution strategy
      const resolution = await applyConflictResolution(item, serverVersion);
      resolutions.push(resolution);
      
      // Update local status
      await updateItemStatus(item.id, 'synced');
      
    } catch (error) {
      console.error(`Conflict resolution failed for ${item.id}:`, error);
      resolutions.push({
        itemId: item.id,
        status: 'failed',
        error: error.message
      });
    }
  }

  return resolutions;
}
```

## User Experience Patterns

### Status Indicators
```typescript
export function ConnectionStatus(): React.JSX.Element {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');
  const [pendingCount, setPendingCount] = useState(0);

  useEffect(() => {
    const connectionManager = ConnectionManager.getInstance();
    
    connectionManager.onStatusChange((online) => {
      setIsOnline(online);
      if (online) {
        // Trigger sync and update status
        startBackgroundSync();
      }
    });

    // Monitor sync queue
    const updatePendingCount = async () => {
      const stats = await getSyncStats();
      setPendingCount(stats.totalPending);
    };

    updatePendingCount();
    const interval = setInterval(updatePendingCount, 5000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${
        isOnline ? 'bg-green-500' : 'bg-red-500'
      }`} />
      <span className="text-sm">
        {isOnline ? 'Online' : 'Offline'}
        {pendingCount > 0 && ` (${pendingCount} pending)`}
      </span>
      {syncStatus === 'syncing' && (
        <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" />
      )}
    </div>
  );
}
```

### Progressive Loading
```typescript
export function CaptureHistory(): React.JSX.Element {
  const [captures, setCaptures] = useState<Capture[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    // Load captures from IndexedDB immediately (offline-available)
    const loadLocalCaptures = async () => {
      const localCaptures = await getCapturesByFrame(frameId, 0, 20);
      setCaptures(localCaptures);
      setIsLoading(false);
    };

    loadLocalCaptures();

    // If online, check for additional server data
    if (navigator.onLine) {
      syncAdditionalCaptures();
    }
  }, [frameId]);

  const syncAdditionalCaptures = async () => {
    try {
      // Fetch any missing captures from server
      const serverCaptures = await fetchServerCaptures(frameId);
      
      // Merge with local data
      const mergedCaptures = mergeCaptures(captures, serverCaptures);
      setCaptures(mergedCaptures);
      
    } catch (error) {
      // Fail silently - local data is still available
      console.warn('Could not sync additional captures:', error);
    }
  };

  return (
    <div>
      {isLoading ? (
        <CaptureHistorySkeleton />
      ) : (
        captures.map(capture => (
          <CaptureItem 
            key={capture.captureId} 
            capture={capture} 
            showSyncStatus={true}  // Show sync status for transparency
          />
        ))
      )}
    </div>
  );
}
```

### Error Recovery Patterns
```typescript
export class OfflineErrorRecovery {
  static async handleStorageError(error: Error, operation: string): Promise<void> {
    console.error(`Storage error during ${operation}:`, error);
    
    // Attempt recovery strategies
    if (error.name === 'QuotaExceededError') {
      await this.cleanupOldData();
      
      // Retry operation once
      try {
        // Re-attempt the failed operation
        await this.retryOperation(operation);
      } catch (retryError) {
        // Show user error with recovery options
        this.showStorageFullError();
      }
    } else if (error.name === 'InvalidStateError') {
      // Database corruption - reinitialize
      await this.reinitializeDatabase();
    }
  }

  private static async cleanupOldData(): Promise<void> {
    // Remove old synced captures (keep originals)
    const oldCaptures = await getOldSyncedCaptures(30); // 30 days old
    
    for (const capture of oldCaptures) {
      // Keep metadata but remove large blobs
      await updateCapture(capture.captureId, {
        processedImageBlob: undefined,
        thumbnailBlob: undefined
      });
    }
  }

  private static async reinitializeDatabase(): Promise<void> {
    // Close existing connection
    closeDB();
    
    // Delete corrupted database
    await deleteDatabase();
    
    // Reinitialize clean database
    await initDB();
    
    // Notify user of data loss and recovery options
    this.showDataRecoveryOptions();
  }
}
```

## Performance Optimization for Offline

### Lazy Loading Strategies
```typescript
export class LazyImageLoader {
  private static cache = new Map<string, Blob>();

  static async loadImage(captureId: string, type: 'original' | 'processed' | 'thumbnail'): Promise<Blob | null> {
    const cacheKey = `${captureId}-${type}`;
    
    // Check memory cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Load from IndexedDB
    const capture = await getCaptureById(captureId);
    if (!capture) return null;

    let blob: Blob | undefined;
    switch (type) {
      case 'original':
        blob = capture.originalImageBlob;
        break;
      case 'processed':
        blob = capture.processedImageBlob;
        break;
      case 'thumbnail':
        blob = capture.thumbnailBlob;
        break;
    }

    if (blob) {
      // Cache in memory for future use
      this.cache.set(cacheKey, blob);
      
      // Implement LRU eviction
      if (this.cache.size > 100) { // Max 100 images in memory
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }
    }

    return blob || null;
  }
}
```

### Efficient Data Structures
```typescript
// Indexed queries for offline performance
export async function getFramesByMachineAndInspector(
  machineSerialNumber: string,
  inspectorName: string
): Promise<Frame[]> {
  const db = await getDB();
  
  // Use compound index for efficient offline queries
  const tx = db.transaction('frames', 'readonly');
  const index = tx.store.index('by-machine-inspector');
  
  // Single index lookup instead of full table scan
  const frames = await index.getAll([machineSerialNumber, inspectorName]);
  
  await tx.done;
  return frames;
}

// Pagination for large datasets
export async function getCapturesPaginated(
  frameId: string,
  offset: number = 0,
  limit: number = 20
): Promise<{ captures: Capture[]; hasMore: boolean }> {
  const db = await getDB();
  const tx = db.transaction('captures', 'readonly');
  const index = tx.store.index('by-frame');
  
  // Use cursor for efficient pagination
  let count = 0;
  let skipped = 0;
  const captures: Capture[] = [];
  
  for await (const cursor of index.iterate(frameId)) {
    if (skipped < offset) {
      skipped++;
      continue;
    }
    
    if (count >= limit) break;
    
    captures.push(cursor.value);
    count++;
  }
  
  // Check if there are more items
  const hasMore = count === limit;
  
  await tx.done;
  return { captures, hasMore };
}
```

## Testing Offline Functionality

### Network Simulation
```typescript
export class NetworkSimulator {
  private originalFetch = window.fetch;
  private isOffline = false;

  simulateOffline(): void {
    this.isOffline = true;
    window.fetch = async () => {
      throw new Error('Network request failed (simulated offline)');
    };
    
    // Trigger offline event
    window.dispatchEvent(new Event('offline'));
  }

  simulateOnline(): void {
    this.isOffline = false;
    window.fetch = this.originalFetch;
    
    // Trigger online event
    window.dispatchEvent(new Event('online'));
  }

  simulateSlowNetwork(delay: number = 2000): void {
    window.fetch = async (input, init) => {
      // Add artificial delay
      await new Promise(resolve => setTimeout(resolve, delay));
      return this.originalFetch(input, init);
    };
  }

  reset(): void {
    window.fetch = this.originalFetch;
    this.isOffline = false;
  }
}
```

### Offline Testing Patterns
```typescript
describe('Offline Functionality', () => {
  let networkSimulator: NetworkSimulator;

  beforeEach(() => {
    networkSimulator = new NetworkSimulator();
  });

  afterEach(() => {
    networkSimulator.reset();
  });

  test('should create frame offline', async () => {
    // Simulate offline environment
    networkSimulator.simulateOffline();

    const frameData = {
      modelNumber: 'TEST-001',
      machineSerialNumber: 'MACHINE-123',
      inspectorName: 'John Doe'
    };

    // Should succeed offline
    const frame = await createFrame(frameData);
    expect(frame.frameId).toBeDefined();
    expect(frame.syncStatus).toBe('pending');

    // Should be stored locally
    const storedFrame = await getFrameById(frame.frameId);
    expect(storedFrame).toEqual(frame);

    // Should be queued for sync
    const syncItems = await getPendingSyncItems();
    expect(syncItems).toHaveLength(1);
    expect(syncItems[0].objectId).toBe(frame.frameId);
  });

  test('should perform detection offline', async () => {
    networkSimulator.simulateOffline();

    const mockImage = createMockImage();
    const result = await performOfflineDetection(mockImage);

    expect(result.detections).toBeDefined();
    expect(result.metrics.totalTime).toBeGreaterThan(0);

    // Should store capture locally
    const captures = await getCapturesByFrame(getCurrentFrameId());
    expect(captures).toHaveLength(1);
    expect(captures[0].syncStatus).toBe('pending');
  });
});
```

This comprehensive offline-first architecture ensures that the Weld Defect Detection System provides reliable, performant operation regardless of network conditions, with intelligent synchronization and conflict resolution when connectivity is restored.